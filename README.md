<div align="center">
    <img src="./media/logo_small.webp"/>
    <h1>Spec Kit</h1>
    <h3><em>Build high-quality software faster.</em></h3>
</div>

<p align="center">
    <strong>An effort to allow organizations to focus on product scenarios rather than writing undifferentiated code with the help of Spec-Driven Development.</strong>
</p>

[![Release](https://github.com/github/spec-kit/actions/workflows/release.yml/badge.svg)](https://github.com/github/spec-kit/actions/workflows/release.yml)

---

## Table of Contents

- [What is Spec-Driven Development?](#what-is-spec-driven-development)
- [Get started](#get-started)
- [Video Overview](#video-overview)
- [Specify CLI Reference](#specify-cli-reference)
- [Core philosophy](#core-philosophy)
- [Development phases](#development-phases)
- [Experimental goals](#experimental-goals)
- [Prerequisites](#prerequisites)
- [Learn more](#learn-more)
- [Detailed process](#detailed-process)
- [Troubleshooting](#troubleshooting)
- [Maintainers](#maintainers)
- [Support](#support)
- [Acknowledgements](#acknowledgements)
- [License](#license)

## What is Spec-Driven Development?

Spec-Driven Development **flips the script** on traditional software development. For decades, code has been king — specifications were just scaffolding we built and discarded once the "real work" of coding began. Spec-Driven Development changes this: **specifications become executable**, directly generating working implementations rather than just guiding them.

## Get started

### 1. Install Specify

#### For Basic Project Initialization (CLI Only)
Initialize your project depending on the coding agent you're using:

```bash
uvx --from git+https://github.com/EmbMicro49/specwebUI.git specify init <PROJECT_NAME>
```

#### For Full Analyzer Functionality
To use the `specify analyze` command, you need to install the full package with dependencies:

```bash
# Clone or download the repository
git clone https://github.com/EmbMicro49/specwebUI.git
cd specwebUI

# Install in development mode (required for analyzer)
pip install -e .

# Now you can use both init and analyze commands
specify init <PROJECT_NAME>
specify analyze [PROJECT_PATH]
```

Alternative installation methods:
```bash
# Install from GitHub with pip
pip install git+https://github.com/EmbMicro49/specwebUI.git

# Or install in virtual environment with uv
uv venv
source .venv/bin/activate  # Linux/Mac
uv pip install git+https://github.com/EmbMicro49/specwebUI.git
```

#### For Existing Repositories
If you already have a project and want to add Spec Kit functionality:

**Option 1: Using Virtual Environment (Recommended)**
```bash
# Navigate to your existing project
cd /path/to/your/existing/project

# Create isolated environment to avoid dependency conflicts
python -m venv .spec-kit-venv
source .spec-kit-venv/bin/activate  # Linux/Mac
# .spec-kit-venv\Scripts\activate  # Windows

# Install Spec Kit in isolated environment
pip install git+https://github.com/EmbMicro49/specwebUI.git

# Initialize Spec Kit in your existing project (adds templates and commands)
specify init --here --ai claude

# Or analyze your existing codebase to generate documentation
specify analyze --ai-enhance --ai-provider anthropic

# Deactivate when done
deactivate
```

**Option 2: Global Installation**
```bash
# Navigate to your existing project
cd /path/to/your/existing/project

# Install Spec Kit globally (may conflict with project dependencies)
pip install git+https://github.com/EmbMicro49/specwebUI.git

# Initialize Spec Kit in your existing project
specify init --here --ai claude

# Analyze your existing codebase
specify analyze --ai-enhance --ai-provider anthropic
```

**Option 3: Using uv (Fastest)**
```bash
# Navigate to your existing project
cd /path/to/your/existing/project

# Create and activate virtual environment with uv
uv venv .spec-kit-venv
source .spec-kit-venv/bin/activate

# Install Spec Kit
uv pip install git+https://github.com/EmbMicro49/specwebUI.git

# Use Spec Kit commands
specify init --here --ai claude
specify analyze --ai-enhance
```

**Option 4: Local Clone and Install (No GitHub Authentication Required)**
```bash
# Navigate to your existing project
cd /path/to/your/existing/project

# Download and extract the repository manually or clone if you have access
# Alternative: Download ZIP from GitHub and extract

# If you have the specwebUI source code locally:
cd /path/to/downloaded/specwebUI

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate  # Windows

# Install in development mode
pip install -e .

# Navigate back to your project and use Spec Kit
cd /path/to/your/existing/project
specify init --here --ai claude
specify analyze --ai-enhance
```

**Option 5: Using uvx (No Installation Required)**
```bash
# Navigate to your existing project
cd /path/to/your/existing/project

# Use uvx to run without installing (if you have local specwebUI source)
uvx --from /path/to/specwebUI specify init --here --ai claude

# Note: analyze command requires full installation, uvx won't work for analyze
```

**What this does:**
- `--here` flag initializes Spec Kit in your current directory without creating a new folder
- Adds Spec Kit templates, slash commands, and workflow files to your existing project
- Preserves your existing code and git history
- Virtual environment prevents dependency conflicts with your existing project
- `specify analyze` generates comprehensive documentation from your existing codebase

### 2. Establish project principles

Use the **`/constitution`** command to create your project's governing principles and development guidelines that will guide all subsequent development.

```bash
/constitution Create principles focused on code quality, testing standards, user experience consistency, and performance requirements
```

### 3. Create the spec

Use the **`/specify`** command to describe what you want to build. Focus on the **what** and **why**, not the tech stack.

```bash
/specify Build an application that can help me organize my photos in separate photo albums. Albums are grouped by date and can be re-organized by dragging and dropping on the main page. Albums are never in other nested albums. Within each album, photos are previewed in a tile-like interface.
```

### 4. Create a technical implementation plan

Use the **`/plan`** command to provide your tech stack and architecture choices.

```bash
/plan The application uses Vite with minimal number of libraries. Use vanilla HTML, CSS, and JavaScript as much as possible. Images are not uploaded anywhere and metadata is stored in a local SQLite database.
```

### 5. Break down into tasks

Use **`/tasks`** to create an actionable task list from your implementation plan.

```bash
/tasks
```

### 6. Execute implementation

Use **`/implement`** to execute all tasks and build your feature according to the plan.

```bash
/implement
```

For detailed step-by-step instructions, see our [comprehensive guide](./spec-driven.md).

## Video Overview

Want to see Spec Kit in action? Watch our [video overview](https://www.youtube.com/watch?v=a9eR1xsfvHg&pp=0gcJCckJAYcqIYzv)!

[![Spec Kit video header](/media/spec-kit-video-header.jpg)](https://www.youtube.com/watch?v=a9eR1xsfvHg&pp=0gcJCckJAYcqIYzv)

## Specify CLI Reference

The `specify` command supports the following options:

### Commands

| Command     | Description                                                    |
|-------------|----------------------------------------------------------------|
| `init`      | Initialize a new Specify project from the latest template      |
| `analyze`   | Analyze an existing codebase and generate Spec Kit documentation |
| `check`     | Check for installed tools (`git`, `claude`, `gemini`, `code`/`code-insiders`, `cursor-agent`, `windsurf`, `qwen`, `opencode`, `codex`) |

### `specify init` Arguments & Options

| Argument/Option        | Type     | Description                                                                  |
|------------------------|----------|------------------------------------------------------------------------------|
| `<project-name>`       | Argument | Name for your new project directory (optional if using `--here`)            |
| `--ai`                 | Option   | AI assistant to use: `claude`, `gemini`, `copilot`, `cursor`, `qwen`, `opencode`, `codex`, or `windsurf` |
| `--script`             | Option   | Script variant to use: `sh` (bash/zsh) or `ps` (PowerShell)                 |
| `--ignore-agent-tools` | Flag     | Skip checks for AI agent tools like Claude Code                             |
| `--no-git`             | Flag     | Skip git repository initialization                                          |
| `--here`               | Flag     | Initialize project in the current directory instead of creating a new one   |
| `--skip-tls`           | Flag     | Skip SSL/TLS verification (not recommended)                                 |
| `--debug`              | Flag     | Enable detailed debug output for troubleshooting                            |
| `--github-token`       | Option   | GitHub token for API requests (or set GH_TOKEN/GITHUB_TOKEN env variable)  |

### `specify analyze` Arguments & Options

| Argument/Option        | Type     | Description                                                                  |
|------------------------|----------|------------------------------------------------------------------------------|
| `<project-path>`       | Argument | Path to the project directory to analyze (defaults to current directory)    |
| `--output`, `-o`       | Option   | Output directory for generated documents (defaults to "spec")               |
| `--name`, `-n`         | Option   | Project name (auto-detected from directory name if not provided)           |
| `--ai-enhance`            | Flag     | Use AI to enhance generated documents                                        |
| `--ai-provider`           | Option   | AI provider for enhancement: `openai`, `anthropic`, `google`, `ollama` (defaults to "openai") |
| `--ai-model`              | Option   | AI model to use for enhancement (defaults to "gpt-4")                       |
| `--enhancement-level`     | Option   | Enhancement intensity: `light`, `moderate`, `comprehensive` (defaults to "moderate") |
| `--cost-limit`            | Option   | Maximum cost limit for AI enhancement in USD (e.g., 5.0 for $5 limit)      |
| `--interactive`           | Flag     | Interactive review mode - approve each document enhancement individually      |
| `--show-diff`             | Flag     | Display before/after differences for enhanced documents                      |
| `--debug`                 | Flag     | Show verbose diagnostic output for troubleshooting                          |

### Examples

```bash
# Basic project initialization
specify init my-project

# Initialize with specific AI assistant
specify init my-project --ai claude

# Initialize with Cursor support
specify init my-project --ai cursor

# Initialize with Windsurf support
specify init my-project --ai windsurf

# Initialize with PowerShell scripts (Windows/cross-platform)
specify init my-project --ai copilot --script ps

# Initialize in current directory
specify init --here --ai copilot

# Skip git initialization
specify init my-project --ai gemini --no-git

# Enable debug output for troubleshooting
specify init my-project --ai claude --debug

# Use GitHub token for API requests (helpful for corporate environments)
specify init my-project --ai claude --github-token ghp_your_token_here

# Check system requirements
specify check

# Analyze existing codebase
specify analyze

# Analyze specific project directory
specify analyze /path/to/project

# Generate docs with custom project name
specify analyze --name "My Awesome Project"

# Output to custom directory
specify analyze --output docs

# Enable AI enhancement with default settings
specify analyze --ai-enhance

# Use Claude for enhancement with comprehensive level
specify analyze --ai-enhance --ai-provider anthropic --ai-model claude-3-opus-20240229 --enhancement-level comprehensive

# AI enhancement with cost limit and interactive review
specify analyze --ai-enhance --cost-limit 5.0 --interactive

# Show differences between original and enhanced documents
specify analyze --ai-enhance --show-diff

# Use local Ollama model for enhancement
specify analyze --ai-enhance --ai-provider ollama --ai-model llama2

# Debug analysis process
specify analyze --debug
```

## AI-Powered Document Enhancement

The `specify analyze` command includes powerful AI enhancement capabilities that can automatically improve the quality, completeness, and clarity of generated documentation.

### Enhancement Features

- **Multiple AI Providers**: Support for OpenAI GPT, Anthropic Claude, Google Gemini, and local Ollama models
- **Enhancement Levels**: Choose from light, moderate, or comprehensive enhancement intensity
- **Cost Control**: Set spending limits to control AI usage costs
- **Interactive Review**: Review and approve each document enhancement individually
- **Diff Visualization**: See exactly what changes the AI made to your documents
- **Smart Caching**: Avoid redundant AI calls with intelligent result caching
- **Consistency Validation**: Ensure enhanced documents remain consistent with each other

### Setting Up AI Enhancement

#### API Keys
Configure API keys for your chosen AI provider:

```bash
# OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# Anthropic Claude
export ANTHROPIC_API_KEY="your-anthropic-api-key"

# Google Gemini
export GOOGLE_API_KEY="your-google-api-key"

# Ollama (local models - no API key needed)
# Just ensure Ollama is running: ollama serve
```

#### Enhancement Configuration File
Create a `.specify-enhance.json` file in your project or home directory for custom settings:

```json
{
  "default_provider": "anthropic",
  "default_model": "claude-3-sonnet-20240229",
  "default_level": "moderate",
  "default_cost_limit": 10.0,
  "custom_prompts": {
    "spec_enhancement": "Focus on user experience and business value",
    "plan_enhancement": "Emphasize scalability and maintainability"
  },
  "sections_to_enhance": {
    "spec.md": ["functional_requirements", "user_stories"],
    "plan.md": ["architecture", "technology_stack"]
  }
}
```

### Enhancement Levels

- **Light**: Basic improvements to clarity and formatting, minimal content additions
- **Moderate**: Balanced enhancement with improved details and missing sections
- **Comprehensive**: Deep enhancement with detailed analysis, examples, and best practices

### Supported AI Providers

| Provider | Models | Notes |
|----------|---------|-------|
| **OpenAI** | gpt-4, gpt-4-turbo, gpt-3.5-turbo | Requires API key, costs apply |
| **Anthropic** | claude-3-opus, claude-3-sonnet, claude-3-haiku | Requires API key, costs apply |
| **Google** | gemini-pro, gemini-pro-vision | Requires API key, costs apply |
| **Ollama** | llama2, codellama, mistral, etc. | Local models, free, requires Ollama installation |

### Available Slash Commands

After running `specify init`, your AI coding agent will have access to these slash commands for structured development:

| Command         | Description                                                           |
|-----------------|-----------------------------------------------------------------------|
| `/constitution` | Create or update project governing principles and development guidelines |
| `/specify`      | Define what you want to build (requirements and user stories)        |
| `/plan`         | Create technical implementation plans with your chosen tech stack     |
| `/tasks`        | Generate actionable task lists for implementation                     |
| `/implement`    | Execute all tasks to build the feature according to the plan         |

### Code Analysis & Documentation Generation

The `specify analyze` command performs automated codebase analysis and generates comprehensive Spec Kit documentation for existing projects.

#### Prerequisites for Analyze Command
- Full installation required (not `uvx`): `pip install -e .` or `pip install git+https://github.com/EmbMicro49/specwebUI.git`
- Python 3.11+ with all analyzer dependencies

#### Quick Start for Analysis

**For New Spec Kit Installation:**
```bash
# 1. Install with full dependencies
git clone https://github.com/EmbMicro49/specwebUI.git && cd specwebUI
pip install -e .

# 2. Analyze any project
specify analyze /path/to/your/project --ai-enhance --ai-provider anthropic
```

**For Existing Project Analysis:**
```bash
# 1. Navigate to your existing project
cd /path/to/your/existing/project

# 2. Install Spec Kit
pip install git+https://github.com/EmbMicro49/specwebUI.git

# 3. Analyze your current project
specify analyze --ai-enhance --cost-limit 5.0 --interactive --show-diff

# 4. Optionally add Spec Kit workflow to your project
specify init --here --ai claude
```

#### Generated Documents

| Generated Document | Description                                                           |
|-------------------|-----------------------------------------------------------------------|
| `spec.md`         | Complete project specification with functional/non-functional requirements, user stories, and acceptance criteria |
| `plan.md`         | Technical implementation plan with architecture decisions, technology stack, and development phases |
| `data-model.md`   | Entity definitions, relationships, validation rules, and data flow patterns |
| `tasks.md`        | Executable task breakdown with dependency ordering and parallel execution markers |
| `research.md`     | Technology decisions, architecture analysis, alternatives considered, and implementation risks |
| `quickstart.md`   | Getting started guide with setup instructions, core workflows, and troubleshooting |

**Supported Languages**: Python, JavaScript, TypeScript, Go
**Framework Detection**: FastAPI, Flask, Django, Express, NestJS, Gin, Echo, React, Vue, Angular
**Project Types**: REST APIs, Web Applications, CLI Tools, Libraries, Microservices

## Core philosophy

Spec-Driven Development is a structured process that emphasizes:

- **Intent-driven development** where specifications define the "_what_" before the "_how_"
- **Rich specification creation** using guardrails and organizational principles
- **Multi-step refinement** rather than one-shot code generation from prompts
- **Heavy reliance** on advanced AI model capabilities for specification interpretation

## Development phases

| Phase | Focus | Key Activities |
|-------|-------|----------------|
| **0-to-1 Development** ("Greenfield") | Generate from scratch | <ul><li>Start with high-level requirements</li><li>Generate specifications</li><li>Plan implementation steps</li><li>Build production-ready applications</li></ul> |
| **Creative Exploration** | Parallel implementations | <ul><li>Explore diverse solutions</li><li>Support multiple technology stacks & architectures</li><li>Experiment with UX patterns</li></ul> |
| **Iterative Enhancement** ("Brownfield") | Brownfield modernization | <ul><li>Add features iteratively</li><li>Modernize legacy systems</li><li>Adapt processes</li></ul> |

## Experimental goals

Our research and experimentation focus on:

### Technology independence

- Create applications using diverse technology stacks
- Validate the hypothesis that Spec-Driven Development is a process not tied to specific technologies, programming languages, or frameworks

### Enterprise constraints

- Demonstrate mission-critical application development
- Incorporate organizational constraints (cloud providers, tech stacks, engineering practices)
- Support enterprise design systems and compliance requirements

### User-centric development

- Build applications for different user cohorts and preferences
- Support various development approaches (from vibe-coding to AI-native development)

### Creative & iterative processes

- Validate the concept of parallel implementation exploration
- Provide robust iterative feature development workflows
- Extend processes to handle upgrades and modernization tasks

## Prerequisites

- **Linux/macOS** (or WSL2 on Windows)
- AI coding agent: [Claude Code](https://www.anthropic.com/claude-code), [GitHub Copilot](https://code.visualstudio.com/), [Gemini CLI](https://github.com/google-gemini/gemini-cli), [Cursor](https://cursor.sh/), [Qwen CLI](https://github.com/QwenLM/qwen-code), [opencode](https://opencode.ai/), [Codex CLI](https://github.com/openai/codex), or [Windsurf](https://windsurf.com/)
- [uv](https://docs.astral.sh/uv/) for package management
- [Python 3.11+](https://www.python.org/downloads/)
- [Git](https://git-scm.com/downloads)

## Learn more

- **[Complete Spec-Driven Development Methodology](./spec-driven.md)** - Deep dive into the full process
- **[Detailed Walkthrough](#detailed-process)** - Step-by-step implementation guide

---

## Detailed process

<details>
<summary>Click to expand the detailed step-by-step walkthrough</summary>

You can use the Specify CLI to bootstrap your project, which will bring in the required artifacts in your environment. Run:

```bash
specify init <project_name>
```

Or initialize in the current directory:

```bash
specify init --here
```

![Specify CLI bootstrapping a new project in the terminal](./media/specify_cli.gif)

You will be prompted to select the AI agent you are using. You can also proactively specify it directly in the terminal:

```bash
specify init <project_name> --ai claude
specify init <project_name> --ai gemini
specify init <project_name> --ai copilot
specify init <project_name> --ai cursor
specify init <project_name> --ai qwen
specify init <project_name> --ai opencode
specify init <project_name> --ai codex
specify init <project_name> --ai windsurf
# Or in current directory:
specify init --here --ai claude
specify init --here --ai codex
```

The CLI will check if you have Claude Code, Gemini CLI, Cursor CLI, Qwen CLI, opencode, or Codex CLI installed. If you do not, or you prefer to get the templates without checking for the right tools, use `--ignore-agent-tools` with your command:

```bash
specify init <project_name> --ai claude --ignore-agent-tools
```

### **STEP 1:** Establish project principles

Go to the project folder and run your AI agent. In our example, we're using `claude`.

![Bootstrapping Claude Code environment](./media/bootstrap-claude-code.gif)

You will know that things are configured correctly if you see the `/constitution`, `/specify`, `/plan`, `/tasks`, and `/implement` commands available.

The first step should be establishing your project's governing principles using the `/constitution` command. This helps ensure consistent decision-making throughout all subsequent development phases:

```text
/constitution Create principles focused on code quality, testing standards, user experience consistency, and performance requirements. Include governance for how these principles should guide technical decisions and implementation choices.
```

This step creates or updates the `/memory/constitution.md` file with your project's foundational guidelines that the AI agent will reference during specification, planning, and implementation phases.

### **STEP 2:** Create project specifications

With your project principles established, you can now create the functional specifications. Use the `/specify` command and then provide the concrete requirements for the project you want to develop.

>[!IMPORTANT]
>Be as explicit as possible about _what_ you are trying to build and _why_. **Do not focus on the tech stack at this point**.

An example prompt:

```text
Develop Taskify, a team productivity platform. It should allow users to create projects, add team members,
assign tasks, comment and move tasks between boards in Kanban style. In this initial phase for this feature,
let's call it "Create Taskify," let's have multiple users but the users will be declared ahead of time, predefined.
I want five users in two different categories, one product manager and four engineers. Let's create three
different sample projects. Let's have the standard Kanban columns for the status of each task, such as "To Do,"
"In Progress," "In Review," and "Done." There will be no login for this application as this is just the very
first testing thing to ensure that our basic features are set up. For each task in the UI for a task card,
you should be able to change the current status of the task between the different columns in the Kanban work board.
You should be able to leave an unlimited number of comments for a particular card. You should be able to, from that task
card, assign one of the valid users. When you first launch Taskify, it's going to give you a list of the five users to pick
from. There will be no password required. When you click on a user, you go into the main view, which displays the list of
projects. When you click on a project, you open the Kanban board for that project. You're going to see the columns.
You'll be able to drag and drop cards back and forth between different columns. You will see any cards that are
assigned to you, the currently logged in user, in a different color from all the other ones, so you can quickly
see yours. You can edit any comments that you make, but you can't edit comments that other people made. You can
delete any comments that you made, but you can't delete comments anybody else made.
```

After this prompt is entered, you should see Claude Code kick off the planning and spec drafting process. Claude Code will also trigger some of the built-in scripts to set up the repository.

Once this step is completed, you should have a new branch created (e.g., `001-create-taskify`), as well as a new specification in the `specs/001-create-taskify` directory.

The produced specification should contain a set of user stories and functional requirements, as defined in the template.

At this stage, your project folder contents should resemble the following:

```text
├── memory
│	 ├── constitution.md
│	 └── constitution_update_checklist.md
├── scripts
│	 ├── check-prerequisites.sh
│	 ├── common.sh
│	 ├── create-new-feature.sh
│	 ├── setup-plan.sh
│	 └── update-claude-md.sh
├── specs
│	 └── 001-create-taskify
│	     └── spec.md
└── templates
    ├── plan-template.md
    ├── spec-template.md
    └── tasks-template.md
```

### **STEP 3:** Functional specification clarification

With the baseline specification created, you can go ahead and clarify any of the requirements that were not captured properly within the first shot attempt. For example, you could use a prompt like this within the same Claude Code session:

```text
For each sample project or project that you create there should be a variable number of tasks between 5 and 15
tasks for each one randomly distributed into different states of completion. Make sure that there's at least
one task in each stage of completion.
```

You should also ask Claude Code to validate the **Review & Acceptance Checklist**, checking off the things that are validated/pass the requirements, and leave the ones that are not unchecked. The following prompt can be used:

```text
Read the review and acceptance checklist, and check off each item in the checklist if the feature spec meets the criteria. Leave it empty if it does not.
```

It's important to use the interaction with Claude Code as an opportunity to clarify and ask questions around the specification - **do not treat its first attempt as final**.

### **STEP 4:** Generate a plan

You can now be specific about the tech stack and other technical requirements. You can use the `/plan` command that is built into the project template with a prompt like this:

```text
We are going to generate this using .NET Aspire, using Postgres as the database. The frontend should use
Blazor server with drag-and-drop task boards, real-time updates. There should be a REST API created with a projects API,
tasks API, and a notifications API.
```

The output of this step will include a number of implementation detail documents, with your directory tree resembling this:

```text
.
├── CLAUDE.md
├── memory
│	 ├── constitution.md
│	 └── constitution_update_checklist.md
├── scripts
│	 ├── check-prerequisites.sh
│	 ├── common.sh
│	 ├── create-new-feature.sh
│	 ├── setup-plan.sh
│	 └── update-claude-md.sh
├── specs
│	 └── 001-create-taskify
│	     ├── contracts
│	     │	 ├── api-spec.json
│	     │	 └── signalr-spec.md
│	     ├── data-model.md
│	     ├── plan.md
│	     ├── quickstart.md
│	     ├── research.md
│	     └── spec.md
└── templates
    ├── CLAUDE-template.md
    ├── plan-template.md
    ├── spec-template.md
    └── tasks-template.md
```

Check the `research.md` document to ensure that the right tech stack is used, based on your instructions. You can ask Claude Code to refine it if any of the components stand out, or even have it check the locally-installed version of the platform/framework you want to use (e.g., .NET).

Additionally, you might want to ask Claude Code to research details about the chosen tech stack if it's something that is rapidly changing (e.g., .NET Aspire, JS frameworks), with a prompt like this:

```text
I want you to go through the implementation plan and implementation details, looking for areas that could
benefit from additional research as .NET Aspire is a rapidly changing library. For those areas that you identify that
require further research, I want you to update the research document with additional details about the specific
versions that we are going to be using in this Taskify application and spawn parallel research tasks to clarify
any details using research from the web.
```

During this process, you might find that Claude Code gets stuck researching the wrong thing - you can help nudge it in the right direction with a prompt like this:

```text
I think we need to break this down into a series of steps. First, identify a list of tasks
that you would need to do during implementation that you're not sure of or would benefit
from further research. Write down a list of those tasks. And then for each one of these tasks,
I want you to spin up a separate research task so that the net results is we are researching
all of those very specific tasks in parallel. What I saw you doing was it looks like you were
researching .NET Aspire in general and I don't think that's gonna do much for us in this case.
That's way too untargeted research. The research needs to help you solve a specific targeted question.
```

>[!NOTE]
>Claude Code might be over-eager and add components that you did not ask for. Ask it to clarify the rationale and the source of the change.

### **STEP 5:** Have Claude Code validate the plan

With the plan in place, you should have Claude Code run through it to make sure that there are no missing pieces. You can use a prompt like this:

```text
Now I want you to go and audit the implementation plan and the implementation detail files.
Read through it with an eye on determining whether or not there is a sequence of tasks that you need
to be doing that are obvious from reading this. Because I don't know if there's enough here. For example,
when I look at the core implementation, it would be useful to reference the appropriate places in the implementation
details where it can find the information as it walks through each step in the core implementation or in the refinement.
```

This helps refine the implementation plan and helps you avoid potential blind spots that Claude Code missed in its planning cycle. Once the initial refinement pass is complete, ask Claude Code to go through the checklist once more before you can get to the implementation.

You can also ask Claude Code (if you have the [GitHub CLI](https://docs.github.com/en/github-cli/github-cli) installed) to go ahead and create a pull request from your current branch to `main` with a detailed description, to make sure that the effort is properly tracked.

>[!NOTE]
>Before you have the agent implement it, it's also worth prompting Claude Code to cross-check the details to see if there are any over-engineered pieces (remember - it can be over-eager). If over-engineered components or decisions exist, you can ask Claude Code to resolve them. Ensure that Claude Code follows the [constitution](base/memory/constitution.md) as the foundational piece that it must adhere to when establishing the plan.

### STEP 6: Implementation

Once ready, use the `/implement` command to execute your implementation plan:

```text
/implement
```

The `/implement` command will:
- Validate that all prerequisites are in place (constitution, spec, plan, and tasks)
- Parse the task breakdown from `tasks.md`
- Execute tasks in the correct order, respecting dependencies and parallel execution markers
- Follow the TDD approach defined in your task plan
- Provide progress updates and handle errors appropriately

>[!IMPORTANT]
>The AI agent will execute local CLI commands (such as `dotnet`, `npm`, etc.) - make sure you have the required tools installed on your machine.

Once the implementation is complete, test the application and resolve any runtime errors that may not be visible in CLI logs (e.g., browser console errors). You can copy and paste such errors back to your AI agent for resolution.

</details>

---

## Troubleshooting

### Analyze Command Issues

#### "Missing analysis dependencies" Error
**Error**: `Error: Missing analysis dependencies. Please install: pip install -e .`
**Cause**: The analyzer requires full package installation, not just `uvx` usage.

**Solution**:
```bash
# Option 1: Clone and install locally (recommended for development)
git clone https://github.com/EmbMicro49/specwebUI.git
cd specwebUI
pip install -e .

# Option 2: Install directly from GitHub
pip install git+https://github.com/EmbMicro49/specwebUI.git

# Then verify installation
specify analyze --help
```

#### "Cannot import name 'DocumentGenerator'" Error
**Error**: `cannot import name 'DocumentGenerator' from 'specify_analyzer.generators'`
**Cause**: Incomplete installation or corrupted package.

**Solution**:
```bash
# Reinstall with force
pip uninstall specify-cli -y
pip install -e . --force-reinstall

# Or clear cache and reinstall
pip cache purge
pip install git+https://github.com/EmbMicro49/specwebUI.git

# If you get authentication errors, use local installation:
# Download the ZIP from GitHub, extract it, then:
cd /path/to/extracted/specwebUI
pip install -e .
```

#### Analyze Command Not Found After Installation
**Error**: `specify: command not found` or `specify analyze` fails

**Solution**:
```bash
# Check if installed correctly
python -c "import specify_cli; print('Import OK')"

# Use direct Python execution if command not found
python -m src.specify_cli analyze

# Or ensure pip bin directory is in PATH
export PATH="$HOME/.local/bin:$PATH"
```

#### Permission Errors During Analysis
**Error**: Permission denied when analyzing projects

**Solution**:
```bash
# Ensure you have read access to project directory
chmod -R +r /path/to/project

# Run with proper permissions
sudo -u $USER specify analyze /path/to/project
```

### Installation Troubleshooting

#### GitHub Authentication Issues
**Error**: `Invalid username or token. Password authentication is not supported`
**Or**: pip install prompts for username/password
**Cause**: GitHub requires token authentication for private repos or organizational access.

**Solutions**:

**Option 1: Use GitHub Personal Access Token (Recommended)**
```bash
# 1. Create a GitHub Personal Access Token:
#    - Go to https://github.com/settings/tokens
#    - Click "Generate new token" → "Generate new token (classic)"
#    - Select scopes: FULL "repo" scope (required for private repositories)
#    - Copy the generated token

# 2a. Install using token directly in URL (bypasses password prompt - MOST RELIABLE):
pip install git+https://<EMAIL>/EmbMicro49/specwebUI.git

# Example:
# pip install git+https://<EMAIL>/EmbMicro49/specwebUI.git

# 2b. If you get password prompt with plain URL:
# pip install git+https://github.com/EmbMicro49/specwebUI.git
# Username for 'https://github.com': EmbMicro49
# Password for 'https://<EMAIL>': your_personal_access_token
#
# IMPORTANT:
# - Use your Personal Access Token as the password, NOT your GitHub password
# - If authentication keeps asking repeatedly, the token likely lacks permissions
# - Use the direct token URL method above instead
```

**Option 2: Set Token as Environment Variable**
```bash
# Set your GitHub token as environment variable
export GITHUB_TOKEN=your_personal_access_token_here

# Install using token from environment
pip install git+https://${GITHUB_TOKEN}@github.com/EmbMicro49/specwebUI.git

# For permanent setup, add to your shell profile (.bashrc, .zshrc, etc.)
echo 'export GITHUB_TOKEN=your_token_here' >> ~/.bashrc
source ~/.bashrc
```

**Option 3: Configure Git Credential Helper**
```bash
# Configure git to store credentials
git config --global credential.helper store

# Make a test git operation to store credentials
git clone https://github.com/EmbMicro49/specwebUI.git temp_clone
# Enter your GitHub username and token when prompted
# Username: your_github_username
# Password: your_personal_access_token

# Clean up and now pip will use stored credentials
rm -rf temp_clone
pip install git+https://github.com/EmbMicro49/specwebUI.git
```

**Option 4: Download ZIP manually (No token needed)**
```bash
# 1. Go to https://github.com/EmbMicro49/specwebUI
# 2. Click "Code" → "Download ZIP"
# 3. Extract and install locally:
cd /path/to/extracted/specwebUI
pip install -e .
```

**Option 5: Use GitHub CLI (if available)**
```bash
# Authenticate with GitHub CLI first
gh auth login

# Clone and install
gh repo clone github/specwebUI
cd specwebUI
pip install -e .
```

**Quick Fix for Repeated Password Prompts**:
If `pip install git+https://github.com/EmbMicro49/specwebUI.git` keeps asking for credentials repeatedly:

**This means authentication failed. Solutions:**

1. **Use direct token URL (most reliable)**:
   ```bash
   pip install git+https://<EMAIL>/EmbMicro49/specwebUI.git
   ```

2. **Check token permissions**: Your token needs FULL "repo" scope for private repositories

3. **When prompted for credentials**:
   - **Username**: `EmbMicro49`
   - **Password**: Your Personal Access Token (NOT GitHub password)

4. **If still failing**: Download ZIP manually from GitHub and install locally

**Token Creation Quick Steps**:
1. Go to https://github.com/settings/tokens
2. Click "Generate new token (classic)"
3. Select FULL "repo" scope (not just public_repo)
4. Copy the generated token (starts with `ghp_`)
5. Use in URL format: `pip install git+https://<EMAIL>/EmbMicro49/specwebUI.git`

#### Virtual Environment Setup
```bash
# Create clean environment
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\activate  # Windows

# Install with all dependencies (use local path if GitHub access issues)
pip install --upgrade pip
pip install git+https://github.com/EmbMicro49/specwebUI.git
# OR if you downloaded locally:
# pip install -e /path/to/specwebUI
```

#### Dependency Conflicts
```bash
# Install in isolated environment with uv
uv venv --python 3.11
source .venv/bin/activate
uv pip install git+https://github.com/EmbMicro49/specwebUI.git
# OR for local installation:
# uv pip install -e /path/to/specwebUI
```

### Git Credential Manager on Linux

If you're having issues with Git authentication on Linux, you can install Git Credential Manager:

```bash
#!/usr/bin/env bash
set -e
echo "Downloading Git Credential Manager v2.6.1..."
wget https://github.com/git-ecosystem/git-credential-manager/releases/download/v2.6.1/gcm-linux_amd64.2.6.1.deb
echo "Installing Git Credential Manager..."
sudo dpkg -i gcm-linux_amd64.2.6.1.deb
echo "Configuring Git to use GCM..."
git config --global credential.helper manager
echo "Cleaning up..."
rm gcm-linux_amd64.2.6.1.deb
```

## Maintainers

- Den Delimarsky ([@localden](https://github.com/localden))
- John Lam ([@jflam](https://github.com/jflam))

## Support

For support, please open a [GitHub issue](https://github.com/github/spec-kit/issues/new). We welcome bug reports, feature requests, and questions about using Spec-Driven Development.

## Acknowledgements

This project is heavily influenced by and based on the work and research of [John Lam](https://github.com/jflam).

## License

This project is licensed under the terms of the MIT open source license. Please refer to the [LICENSE](./LICENSE) file for the full terms.
