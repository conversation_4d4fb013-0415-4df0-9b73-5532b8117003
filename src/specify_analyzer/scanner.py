"""Repository scanner for traversing and analyzing codebases."""

import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import mimetypes


class FileType(Enum):
    """Supported file types for analysis."""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    GO = "go"
    JSON = "json"
    YAML = "yaml"
    MARKDOWN = "markdown"
    SQL = "sql"
    DOCKERFILE = "dockerfile"
    CONFIG = "config"
    UNKNOWN = "unknown"


@dataclass
class FileInfo:
    """Information about a scanned file."""
    path: Path
    file_type: FileType
    size: int
    lines: int
    is_test: bool
    is_config: bool
    framework_hints: List[str]


@dataclass
class ScanResult:
    """Results of repository scanning."""
    files: List[FileInfo]
    project_type: str  # web, mobile, cli, library, etc.
    languages: Set[str]
    frameworks: Set[str]
    test_frameworks: Set[str]
    databases: Set[str]
    build_tools: Set[str]
    package_managers: Set[str]


class RepositoryScanner:
    """Scans repository structure and identifies file types and patterns."""

    # File extension mappings
    EXTENSIONS = {
        ".py": FileType.PYTHON,
        ".js": FileType.JAVASCRIPT,
        ".jsx": FileType.JAVASCRIPT,
        ".ts": FileType.TYPESCRIPT,
        ".tsx": FileType.TYPESCRIPT,
        ".go": FileType.GO,
        ".json": FileType.JSON,
        ".yaml": FileType.YAML,
        ".yml": FileType.YAML,
        ".md": FileType.MARKDOWN,
        ".sql": FileType.SQL,
    }

    # Test file patterns
    TEST_PATTERNS = [
        "test_", "_test", ".test.", ".spec.", "tests/", "test/",
        "__tests__/", "spec/", "specs/"
    ]

    # Config file patterns
    CONFIG_PATTERNS = [
        "config", ".env", "docker", "Dockerfile", "requirements",
        "package.json", "pyproject.toml", "setup.py", "Cargo.toml",
        "go.mod", "tsconfig", "webpack", "babel", "eslint", "prettier"
    ]

    # Framework detection patterns
    FRAMEWORK_PATTERNS = {
        "fastapi": ["from fastapi", "import fastapi", "FastAPI("],
        "flask": ["from flask", "import flask", "Flask("],
        "django": ["from django", "import django", "DJANGO_SETTINGS"],
        "express": ["express()", "require('express')", "import express"],
        "react": ["import React", "from 'react'", "jsx", "tsx"],
        "vue": ["import Vue", "from 'vue'", ".vue"],
        "angular": ["@angular", "import { Component }"],
        "spring": ["@SpringBootApplication", "import org.springframework"],
        "gin": ["gin.Engine", "github.com/gin-gonic/gin"],
        "echo": ["echo.New()", "github.com/labstack/echo"],
        "fiber": ["fiber.New()", "github.com/gofiber/fiber"],
    }

    def __init__(self, repo_path: Path):
        """Initialize scanner with repository path."""
        self.repo_path = Path(repo_path)
        self.ignore_patterns = {
            ".git", "__pycache__", "node_modules", ".venv", "venv",
            "dist", "build", ".cache", ".pytest_cache", "target",
            "vendor", ".idea", ".vscode"
        }

    def scan(self) -> ScanResult:
        """Scan the repository and return analysis results."""
        files = []

        for file_path in self._traverse_directory():
            file_info = self._analyze_file(file_path)
            if file_info:
                files.append(file_info)

        # Analyze project patterns
        project_type = self._detect_project_type(files)
        languages = self._detect_languages(files)
        frameworks = self._detect_frameworks(files)
        test_frameworks = self._detect_test_frameworks(files)
        databases = self._detect_databases(files)
        build_tools = self._detect_build_tools(files)
        package_managers = self._detect_package_managers(files)

        return ScanResult(
            files=files,
            project_type=project_type,
            languages=languages,
            frameworks=frameworks,
            test_frameworks=test_frameworks,
            databases=databases,
            build_tools=build_tools,
            package_managers=package_managers
        )

    def _traverse_directory(self) -> List[Path]:
        """Traverse directory and yield file paths."""
        file_paths = []

        for root, dirs, files in os.walk(self.repo_path):
            # Filter out ignored directories
            dirs[:] = [d for d in dirs if d not in self.ignore_patterns]

            for file in files:
                file_path = Path(root) / file
                if not self._should_ignore_file(file_path):
                    file_paths.append(file_path)

        return file_paths

    def _should_ignore_file(self, file_path: Path) -> bool:
        """Check if file should be ignored."""
        # Ignore binary files and very large files
        try:
            if file_path.stat().st_size > 10 * 1024 * 1024:  # 10MB
                return True

            # Check if it's a binary file
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type and not mime_type.startswith('text/'):
                return True

        except (OSError, PermissionError):
            return True

        return False

    def _analyze_file(self, file_path: Path) -> Optional[FileInfo]:
        """Analyze a single file and return FileInfo."""
        try:
            # Determine file type
            file_type = self._get_file_type(file_path)

            # Get file stats
            stat = file_path.stat()
            size = stat.st_size

            # Count lines for text files
            lines = 0
            framework_hints = []

            if file_type != FileType.UNKNOWN:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        lines = content.count('\n') + 1
                        framework_hints = self._detect_framework_hints(content)
                except (UnicodeDecodeError, PermissionError):
                    pass

            # Determine if it's a test or config file
            is_test = self._is_test_file(file_path)
            is_config = self._is_config_file(file_path)

            return FileInfo(
                path=file_path,
                file_type=file_type,
                size=size,
                lines=lines,
                is_test=is_test,
                is_config=is_config,
                framework_hints=framework_hints
            )

        except (OSError, PermissionError):
            return None

    def _get_file_type(self, file_path: Path) -> FileType:
        """Determine file type from extension and name."""
        # Check by extension
        extension = file_path.suffix.lower()
        if extension in self.EXTENSIONS:
            return self.EXTENSIONS[extension]

        # Special cases
        name = file_path.name.lower()
        if name in ["dockerfile", "dockerfile.dev", "dockerfile.prod"]:
            return FileType.DOCKERFILE

        if name.startswith("docker"):
            return FileType.CONFIG

        return FileType.UNKNOWN

    def _is_test_file(self, file_path: Path) -> bool:
        """Check if file is a test file."""
        path_str = str(file_path).lower()
        return any(pattern in path_str for pattern in self.TEST_PATTERNS)

    def _is_config_file(self, file_path: Path) -> bool:
        """Check if file is a configuration file."""
        name = file_path.name.lower()
        return any(pattern in name for pattern in self.CONFIG_PATTERNS)

    def _detect_framework_hints(self, content: str) -> List[str]:
        """Detect framework usage from file content."""
        hints = []
        content_lower = content.lower()

        for framework, patterns in self.FRAMEWORK_PATTERNS.items():
            if any(pattern.lower() in content_lower for pattern in patterns):
                hints.append(framework)

        return hints

    def _detect_project_type(self, files: List[FileInfo]) -> str:
        """Detect overall project type."""
        has_frontend = any(f.file_type in [FileType.JAVASCRIPT, FileType.TYPESCRIPT]
                          and not f.is_test for f in files)
        has_backend = any(f.file_type in [FileType.PYTHON, FileType.GO]
                         and not f.is_test for f in files)
        has_mobile = any("ios" in str(f.path) or "android" in str(f.path)
                        for f in files)
        has_cli = any("cli" in str(f.path) or "main.py" in str(f.path)
                     or "main.go" in str(f.path) for f in files)

        if has_mobile:
            return "mobile"
        elif has_frontend and has_backend:
            return "web"
        elif has_cli:
            return "cli"
        elif has_backend:
            return "api"
        elif has_frontend:
            return "frontend"
        else:
            return "library"

    def _detect_languages(self, files: List[FileInfo]) -> Set[str]:
        """Detect programming languages used."""
        languages = set()
        type_map = {
            FileType.PYTHON: "Python",
            FileType.JAVASCRIPT: "JavaScript",
            FileType.TYPESCRIPT: "TypeScript",
            FileType.GO: "Go",
        }

        for file in files:
            if file.file_type in type_map and not file.is_test:
                languages.add(type_map[file.file_type])

        return languages

    def _detect_frameworks(self, files: List[FileInfo]) -> Set[str]:
        """Detect frameworks used in the project."""
        frameworks = set()

        for file in files:
            for hint in file.framework_hints:
                frameworks.add(hint)

        return frameworks

    def _detect_test_frameworks(self, files: List[FileInfo]) -> Set[str]:
        """Detect testing frameworks."""
        test_frameworks = set()

        test_patterns = {
            "pytest": ["pytest", "conftest.py"],
            "unittest": ["unittest", "test_*.py"],
            "jest": ["jest", ".test.js", ".spec.js"],
            "mocha": ["mocha", "describe(", "it("],
            "go test": ["_test.go", "testing.T"],
        }

        for file in files:
            if file.is_test:
                content_str = str(file.path)
                for framework, patterns in test_patterns.items():
                    if any(pattern in content_str for pattern in patterns):
                        test_frameworks.add(framework)

        return test_frameworks

    def _detect_databases(self, files: List[FileInfo]) -> Set[str]:
        """Detect database usage."""
        databases = set()

        db_patterns = {
            "postgresql": ["psycopg", "postgresql://", "postgres://"],
            "mysql": ["pymysql", "mysql://"],
            "sqlite": ["sqlite3", "sqlite://"],
            "mongodb": ["pymongo", "mongodb://"],
            "redis": ["redis-py", "redis://"],
        }

        for file in files:
            if file.framework_hints:
                for db, patterns in db_patterns.items():
                    if any(pattern in hint for hint in file.framework_hints
                          for pattern in patterns):
                        databases.add(db)

        return databases

    def _detect_build_tools(self, files: List[FileInfo]) -> Set[str]:
        """Detect build tools and task runners."""
        build_tools = set()

        build_files = {
            "webpack": ["webpack.config.js"],
            "vite": ["vite.config.js", "vite.config.ts"],
            "rollup": ["rollup.config.js"],
            "gradle": ["build.gradle"],
            "maven": ["pom.xml"],
            "make": ["Makefile"],
            "docker": ["Dockerfile"],
        }

        for file in files:
            filename = file.path.name
            for tool, patterns in build_files.items():
                if any(pattern in filename for pattern in patterns):
                    build_tools.add(tool)

        return build_tools

    def _detect_package_managers(self, files: List[FileInfo]) -> Set[str]:
        """Detect package managers."""
        package_managers = set()

        manager_files = {
            "npm": ["package.json", "package-lock.json"],
            "yarn": ["yarn.lock"],
            "pnpm": ["pnpm-lock.yaml"],
            "pip": ["requirements.txt", "pyproject.toml"],
            "poetry": ["poetry.lock"],
            "go mod": ["go.mod", "go.sum"],
            "cargo": ["Cargo.toml", "Cargo.lock"],
        }

        for file in files:
            filename = file.path.name
            for manager, patterns in manager_files.items():
                if any(pattern in filename for pattern in patterns):
                    package_managers.add(manager)

        return package_managers