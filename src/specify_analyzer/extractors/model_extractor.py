"""Data model extractor for identifying entities, schemas, and data structures."""

from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

from ..parsers.base_parser import ParsedFile


class FieldType(Enum):
    """Types of model fields."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATE = "date"
    DATETIME = "datetime"
    ARRAY = "array"
    OBJECT = "object"
    REFERENCE = "reference"
    UNKNOWN = "unknown"


@dataclass
class ModelField:
    """Represents a field in a data model."""
    name: str
    field_type: FieldType
    type_annotation: Optional[str]
    required: bool
    default_value: Optional[str]
    description: Optional[str]
    constraints: Dict[str, Any]
    relationships: List[str]


@dataclass
class DataModel:
    """Represents a data model/entity."""
    name: str
    fields: List[ModelField]
    description: Optional[str]
    base_classes: List[str]
    framework: str
    file_path: Path
    line_number: int
    table_name: Optional[str]
    indexes: List[str]
    relationships: Dict[str, str]
    validations: List[str]


@dataclass
class ModelSchema:
    """Represents the complete data model schema."""
    models: List[DataModel]
    relationships: Dict[str, List[str]]
    inheritance_tree: Dict[str, List[str]]
    database_type: Optional[str]
    migration_files: List[Path]


class ModelExtractor:
    """Extracts data models and schemas from parsed code files."""

    def __init__(self):
        self.model_frameworks = {
            'sqlalchemy': ['SQLAlchemyBase', 'Base', 'DeclarativeBase'],
            'django': ['models.Model', 'Model'],
            'pydantic': ['BaseModel', 'BaseSettings'],
            'dataclass': ['@dataclass', 'dataclass'],
            'mongoose': ['Schema', 'mongoose.Schema'],
            'sequelize': ['Sequelize.Model', 'Model'],
            'gorm': ['gorm.Model'],
            'typeorm': ['Entity', '@Entity'],
        }

        self.type_mappings = {
            # Python types
            'str': FieldType.STRING,
            'string': FieldType.STRING,
            'int': FieldType.INTEGER,
            'integer': FieldType.INTEGER,
            'float': FieldType.FLOAT,
            'bool': FieldType.BOOLEAN,
            'boolean': FieldType.BOOLEAN,
            'date': FieldType.DATE,
            'datetime': FieldType.DATETIME,
            'list': FieldType.ARRAY,
            'dict': FieldType.OBJECT,

            # JavaScript/TypeScript types
            'string': FieldType.STRING,
            'number': FieldType.FLOAT,
            'boolean': FieldType.BOOLEAN,
            'array': FieldType.ARRAY,
            'object': FieldType.OBJECT,

            # Go types
            'string': FieldType.STRING,
            'int': FieldType.INTEGER,
            'int32': FieldType.INTEGER,
            'int64': FieldType.INTEGER,
            'float32': FieldType.FLOAT,
            'float64': FieldType.FLOAT,
            'bool': FieldType.BOOLEAN,
            'time.Time': FieldType.DATETIME,

            # SQL types
            'varchar': FieldType.STRING,
            'text': FieldType.STRING,
            'integer': FieldType.INTEGER,
            'decimal': FieldType.FLOAT,
            'timestamp': FieldType.DATETIME,
        }

    def extract_from_files(self, parsed_files: List[ParsedFile]) -> ModelSchema:
        """Extract data model schema from multiple parsed files."""
        models = []
        all_relationships = {}
        inheritance_tree = {}

        for parsed_file in parsed_files:
            file_models = self._extract_models_from_file(parsed_file)
            models.extend(file_models)

        # Build relationship and inheritance maps
        for model in models:
            # Inheritance relationships
            if model.base_classes:
                for base in model.base_classes:
                    if base not in inheritance_tree:
                        inheritance_tree[base] = []
                    inheritance_tree[base].append(model.name)

            # Field relationships
            for field in model.fields:
                if field.relationships:
                    if model.name not in all_relationships:
                        all_relationships[model.name] = []
                    all_relationships[model.name].extend(field.relationships)

        # Detect database type
        database_type = self._detect_database_type(parsed_files)

        # Find migration files
        migration_files = self._find_migration_files(parsed_files)

        return ModelSchema(
            models=models,
            relationships=all_relationships,
            inheritance_tree=inheritance_tree,
            database_type=database_type,
            migration_files=migration_files
        )

    def _extract_models_from_file(self, parsed_file: ParsedFile) -> List[DataModel]:
        """Extract data models from a single parsed file."""
        models = []

        # Get parser-specific model extraction
        from ..parsers.python_parser import PythonParser
        from ..parsers.javascript_parser import JavaScriptParser
        from ..parsers.typescript_parser import TypeScriptParser
        from ..parsers.go_parser import GoParser

        parser = None
        if parsed_file.path.suffix == '.py':
            parser = PythonParser()
        elif parsed_file.path.suffix in ['.js', '.jsx']:
            parser = JavaScriptParser()
        elif parsed_file.path.suffix in ['.ts', '.tsx']:
            parser = TypeScriptParser()
        elif parsed_file.path.suffix == '.go':
            parser = GoParser()

        if parser:
            raw_models = parser.extract_data_models(parsed_file)
            for model_data in raw_models:
                model = self._create_model_from_data(model_data, parsed_file)
                if model:
                    models.append(model)

        # Also check for models in classes
        for cls in parsed_file.classes:
            if self._is_model_class(cls, parsed_file):
                model = self._extract_model_from_class(cls, parsed_file)
                if model:
                    models.append(model)

        return models

    def _create_model_from_data(self, data: Dict[str, Any], parsed_file: ParsedFile) -> Optional[DataModel]:
        """Create DataModel from raw model data."""
        try:
            fields = []
            for field_data in data.get('fields', []):
                field = self._create_field_from_data(field_data)
                if field:
                    fields.append(field)

            framework = self._detect_model_framework(data.get('base_classes', []))

            return DataModel(
                name=data.get('name', 'Unknown'),
                fields=fields,
                description=data.get('docstring'),
                base_classes=data.get('base_classes', []),
                framework=framework,
                file_path=parsed_file.path,
                line_number=data.get('line_number', 0),
                table_name=self._extract_table_name(data),
                indexes=[],
                relationships=self._extract_model_relationships(data),
                validations=[]
            )
        except Exception:
            return None

    def _create_field_from_data(self, field_data: Dict[str, Any]) -> Optional[ModelField]:
        """Create ModelField from raw field data."""
        try:
            field_type = self._map_type_to_enum(field_data.get('type', 'unknown'))

            return ModelField(
                name=field_data.get('name', 'unknown'),
                field_type=field_type,
                type_annotation=field_data.get('type'),
                required=field_data.get('required', True),
                default_value=field_data.get('default'),
                description=field_data.get('description'),
                constraints=self._extract_field_constraints(field_data),
                relationships=self._extract_field_relationships(field_data)
            )
        except Exception:
            return None

    def _is_model_class(self, cls, parsed_file: ParsedFile) -> bool:
        """Check if a class is a data model."""
        # Check base classes
        for framework, base_classes in self.model_frameworks.items():
            if any(base in cls.base_classes for base in base_classes):
                return True

        # Check decorators
        if any('@dataclass' in dec for dec in cls.decorators):
            return True

        # Check naming patterns
        model_suffixes = ['Model', 'Entity', 'Schema', 'Data']
        if any(cls.name.endswith(suffix) for suffix in model_suffixes):
            return True

        return False

    def _extract_model_from_class(self, cls, parsed_file: ParsedFile) -> Optional[DataModel]:
        """Extract DataModel from a class definition."""
        fields = []

        # Extract fields from attributes
        for attr in cls.attributes:
            field = self._create_field_from_attribute(attr)
            if field:
                fields.append(field)

        # Extract fields from method signatures (like __init__)
        for method in cls.methods:
            if method.name == '__init__':
                init_fields = self._extract_fields_from_init(method)
                fields.extend(init_fields)

        framework = self._detect_model_framework(cls.base_classes)

        return DataModel(
            name=cls.name,
            fields=fields,
            description=cls.docstring,
            base_classes=cls.base_classes,
            framework=framework,
            file_path=parsed_file.path,
            line_number=cls.line_number,
            table_name=self._infer_table_name(cls.name),
            indexes=[],
            relationships={},
            validations=[]
        )

    def _create_field_from_attribute(self, attr: str) -> Optional[ModelField]:
        """Create ModelField from class attribute string."""
        # Parse attribute like "name: str = 'default'"
        if ':' in attr:
            name_part, type_part = attr.split(':', 1)
            name = name_part.strip()

            # Extract type and default value
            if '=' in type_part:
                type_str, default_str = type_part.split('=', 1)
                type_str = type_str.strip()
                default_value = default_str.strip()
            else:
                type_str = type_part.strip()
                default_value = None

            field_type = self._map_type_to_enum(type_str)
            required = default_value is None

            return ModelField(
                name=name,
                field_type=field_type,
                type_annotation=type_str,
                required=required,
                default_value=default_value,
                description=None,
                constraints={},
                relationships=[]
            )

        return None

    def _extract_fields_from_init(self, init_method) -> List[ModelField]:
        """Extract fields from __init__ method parameters."""
        fields = []

        for arg in init_method.args[1:]:  # Skip 'self'
            if ':' in arg:
                name_part, type_part = arg.split(':', 1)
                name = name_part.strip()

                # Check for default value
                if '=' in type_part:
                    type_str, default_str = type_part.split('=', 1)
                    type_str = type_str.strip()
                    default_value = default_str.strip()
                    required = False
                else:
                    type_str = type_part.strip()
                    default_value = None
                    required = True

                field_type = self._map_type_to_enum(type_str)

                fields.append(ModelField(
                    name=name,
                    field_type=field_type,
                    type_annotation=type_str,
                    required=required,
                    default_value=default_value,
                    description=None,
                    constraints={},
                    relationships=[]
                ))

        return fields

    def _map_type_to_enum(self, type_str: str) -> FieldType:
        """Map type string to FieldType enum."""
        type_lower = type_str.lower().strip()

        # Direct mapping
        if type_lower in self.type_mappings:
            return self.type_mappings[type_lower]

        # Pattern matching
        if 'string' in type_lower or 'str' in type_lower:
            return FieldType.STRING
        elif 'int' in type_lower or 'number' in type_lower:
            return FieldType.INTEGER
        elif 'float' in type_lower or 'decimal' in type_lower:
            return FieldType.FLOAT
        elif 'bool' in type_lower:
            return FieldType.BOOLEAN
        elif 'date' in type_lower or 'time' in type_lower:
            if 'datetime' in type_lower or 'timestamp' in type_lower:
                return FieldType.DATETIME
            else:
                return FieldType.DATE
        elif 'list' in type_lower or 'array' in type_lower:
            return FieldType.ARRAY
        elif 'dict' in type_lower or 'object' in type_lower:
            return FieldType.OBJECT
        elif type_str and type_str[0].isupper():
            # Capitalized types are likely references to other models
            return FieldType.REFERENCE

        return FieldType.UNKNOWN

    def _detect_model_framework(self, base_classes: List[str]) -> str:
        """Detect which framework is used for the model."""
        for framework, framework_bases in self.model_frameworks.items():
            if any(base in base_classes for base in framework_bases):
                return framework

        return 'unknown'

    def _extract_table_name(self, data: Dict[str, Any]) -> Optional[str]:
        """Extract table name from model data."""
        # Look for explicit table name in metadata
        # This would be framework-specific
        return None

    def _infer_table_name(self, class_name: str) -> str:
        """Infer database table name from class name."""
        # Convert CamelCase to snake_case and pluralize
        import re

        # Insert underscores before capital letters
        snake_case = re.sub('([a-z0-9])([A-Z])', r'\1_\2', class_name)
        snake_case = snake_case.lower()

        # Simple pluralization
        if snake_case.endswith('y'):
            return snake_case[:-1] + 'ies'
        elif snake_case.endswith(('s', 'sh', 'ch', 'x', 'z')):
            return snake_case + 'es'
        else:
            return snake_case + 's'

    def _extract_model_relationships(self, data: Dict[str, Any]) -> Dict[str, str]:
        """Extract model-level relationships."""
        relationships = {}

        # This would be framework-specific logic
        # For now, return empty dict

        return relationships

    def _extract_field_constraints(self, field_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract field constraints from field data."""
        constraints = {}

        # Extract common constraints
        if 'max_length' in field_data:
            constraints['max_length'] = field_data['max_length']
        if 'min_length' in field_data:
            constraints['min_length'] = field_data['min_length']
        if 'max_value' in field_data:
            constraints['max_value'] = field_data['max_value']
        if 'min_value' in field_data:
            constraints['min_value'] = field_data['min_value']

        return constraints

    def _extract_field_relationships(self, field_data: Dict[str, Any]) -> List[str]:
        """Extract field-level relationships."""
        relationships = []

        # Look for foreign key relationships in type annotations
        field_type = field_data.get('type', '')
        if field_type and field_type[0].isupper():
            # Capitalized type likely indicates a relationship
            relationships.append(field_type)

        return relationships

    def _detect_database_type(self, parsed_files: List[ParsedFile]) -> Optional[str]:
        """Detect database type from parsed files."""
        db_imports = {
            'postgresql': ['psycopg2', 'asyncpg', 'postgres'],
            'mysql': ['pymysql', 'mysql', 'aiomysql'],
            'sqlite': ['sqlite3', 'aiosqlite'],
            'mongodb': ['pymongo', 'motor', 'mongoengine'],
            'redis': ['redis', 'aioredis'],
        }

        for parsed_file in parsed_files:
            for imp in parsed_file.imports:
                for db_type, db_modules in db_imports.items():
                    if any(module in imp.module.lower() for module in db_modules):
                        return db_type

        return None

    def _find_migration_files(self, parsed_files: List[ParsedFile]) -> List[Path]:
        """Find database migration files."""
        migration_files = []

        for parsed_file in parsed_files:
            path_str = str(parsed_file.path).lower()
            if any(keyword in path_str for keyword in ['migration', 'migrate', 'alembic']):
                migration_files.append(parsed_file.path)

        return migration_files