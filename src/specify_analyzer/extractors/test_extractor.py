"""Test case extractor for identifying test patterns, coverage, and testing strategies."""

from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

from ..parsers.base_parser import ParsedFile


class TestType(Enum):
    """Types of tests."""
    UNIT = "unit"
    INTEGRATION = "integration"
    FUNCTIONAL = "functional"
    END_TO_END = "e2e"
    PERFORMANCE = "performance"
    SECURITY = "security"
    API = "api"
    UNKNOWN = "unknown"


@dataclass
class TestCase:
    """Represents a single test case."""
    name: str
    test_type: TestType
    description: Optional[str]
    target_function: Optional[str]
    target_class: Optional[str]
    framework: str
    file_path: Path
    line_number: int
    assertions: List[str]
    setup_methods: List[str]
    teardown_methods: List[str]
    fixtures: List[str]
    mocks: List[str]
    tags: List[str]


@dataclass
class TestSuite:
    """Represents a test suite or test class."""
    name: str
    test_cases: List[TestCase]
    setup_class: Optional[str]
    teardown_class: Optional[str]
    shared_fixtures: List[str]
    file_path: Path
    framework: str


@dataclass
class TestCoverage:
    """Represents test coverage information."""
    covered_functions: Set[str]
    covered_classes: Set[str]
    uncovered_functions: Set[str]
    uncovered_classes: Set[str]
    coverage_percentage: Optional[float]


@dataclass
class TestSchema:
    """Represents the complete test schema."""
    test_suites: List[TestSuite]
    test_cases: List[TestCase]
    frameworks: Set[str]
    test_types: Set[TestType]
    coverage: TestCoverage
    config_files: List[Path]
    test_runners: List[str]


class TestExtractor:
    """Extracts test cases and patterns from parsed code files."""

    def __init__(self):
        self.test_frameworks = {
            'pytest': {
                'patterns': ['test_', 'pytest', '@pytest', 'conftest.py'],
                'fixtures': ['@pytest.fixture', 'fixture'],
                'assertions': ['assert ', 'pytest.raises'],
                'markers': ['@pytest.mark']
            },
            'unittest': {
                'patterns': ['unittest', 'TestCase', 'test_', 'setUp', 'tearDown'],
                'assertions': ['self.assert', 'self.assertEqual', 'self.assertTrue'],
                'setup': ['setUp', 'setUpClass'],
                'teardown': ['tearDown', 'tearDownClass']
            },
            'jest': {
                'patterns': ['describe(', 'it(', 'test(', 'expect('],
                'setup': ['beforeEach', 'beforeAll'],
                'teardown': ['afterEach', 'afterAll'],
                'mocks': ['jest.mock', 'jest.fn', 'jest.spyOn']
            },
            'mocha': {
                'patterns': ['describe(', 'it(', 'before(', 'after('],
                'assertions': ['expect(', 'should'],
                'setup': ['before(', 'beforeEach('],
                'teardown': ['after(', 'afterEach(']
            },
            'go test': {
                'patterns': ['func Test', 'testing.T', 't.Error', 't.Fatal'],
                'assertions': ['t.Error', 't.Errorf', 't.Fatal', 't.Fatalf'],
                'setup': ['TestMain'],
                'benchmarks': ['func Benchmark', 'testing.B']
            },
            'rspec': {
                'patterns': ['describe ', 'it ', 'context ', 'expect('],
                'setup': ['before(', 'before_each'],
                'teardown': ['after(', 'after_each'],
                'mocks': ['double(', 'allow(', 'expect(']
            }
        }

        self.test_type_patterns = {
            TestType.UNIT: ['unit', 'test_unit', '/unit/', 'spec/unit'],
            TestType.INTEGRATION: ['integration', 'test_integration', '/integration/', 'spec/integration'],
            TestType.FUNCTIONAL: ['functional', 'test_functional', '/functional/', 'spec/functional'],
            TestType.END_TO_END: ['e2e', 'end_to_end', 'end2end', '/e2e/', 'spec/e2e'],
            TestType.PERFORMANCE: ['performance', 'benchmark', 'load', 'stress'],
            TestType.SECURITY: ['security', 'auth', 'permission', 'vulnerability'],
            TestType.API: ['api', 'endpoint', 'rest', 'graphql']
        }

    def extract_from_files(self, parsed_files: List[ParsedFile]) -> TestSchema:
        """Extract test schema from multiple parsed files."""
        test_suites = []
        all_test_cases = []
        frameworks = set()
        test_types = set()

        # Filter test files
        test_files = [f for f in parsed_files if self._is_test_file(f)]

        for test_file in test_files:
            file_test_cases = self._extract_test_cases_from_file(test_file)
            all_test_cases.extend(file_test_cases)

            # Group test cases into suites
            suite = self._create_test_suite_from_file(test_file, file_test_cases)
            if suite:
                test_suites.append(suite)

            # Collect frameworks and types
            for test_case in file_test_cases:
                frameworks.add(test_case.framework)
                test_types.add(test_case.test_type)

        # Calculate coverage
        coverage = self._calculate_coverage(all_test_cases, parsed_files)

        # Find configuration files
        config_files = self._find_test_config_files(parsed_files)

        # Identify test runners
        test_runners = self._identify_test_runners(parsed_files)

        return TestSchema(
            test_suites=test_suites,
            test_cases=all_test_cases,
            frameworks=frameworks,
            test_types=test_types,
            coverage=coverage,
            config_files=config_files,
            test_runners=test_runners
        )

    def _is_test_file(self, parsed_file: ParsedFile) -> bool:
        """Check if a file is a test file."""
        path_str = str(parsed_file.path).lower()

        # Check path patterns
        test_patterns = [
            'test_', '_test', 'test/', 'tests/', '__tests__/',
            'spec/', 'specs/', '.test.', '.spec.',
            'test.py', 'test.js', 'test.ts', 'test.go'
        ]

        return any(pattern in path_str for pattern in test_patterns)

    def _extract_test_cases_from_file(self, parsed_file: ParsedFile) -> List[TestCase]:
        """Extract test cases from a single test file."""
        test_cases = []

        # Detect framework
        framework = self._detect_test_framework(parsed_file)

        # Extract from functions
        for func in parsed_file.functions:
            if self._is_test_function(func.name, framework):
                test_case = self._create_test_case_from_function(func, parsed_file, framework)
                if test_case:
                    test_cases.append(test_case)

        # Extract from classes
        for cls in parsed_file.classes:
            if self._is_test_class(cls.name, framework):
                for method in cls.methods:
                    if self._is_test_method(method.name, framework):
                        test_case = self._create_test_case_from_method(method, cls, parsed_file, framework)
                        if test_case:
                            test_cases.append(test_case)

        return test_cases

    def _detect_test_framework(self, parsed_file: ParsedFile) -> str:
        """Detect which test framework is being used."""
        # Check imports
        for imp in parsed_file.imports:
            for framework, patterns in self.test_frameworks.items():
                if any(pattern in imp.module.lower() for pattern in patterns['patterns']):
                    return framework

        # Check file content patterns
        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            for framework, patterns in self.test_frameworks.items():
                if any(pattern in content for pattern in patterns['patterns']):
                    return framework

        except Exception:
            pass

        # Fallback based on file extension
        if parsed_file.path.suffix == '.py':
            return 'pytest'  # Default for Python
        elif parsed_file.path.suffix in ['.js', '.ts', '.jsx', '.tsx']:
            return 'jest'  # Default for JavaScript/TypeScript
        elif parsed_file.path.suffix == '.go':
            return 'go test'

        return 'unknown'

    def _is_test_function(self, func_name: str, framework: str) -> bool:
        """Check if a function is a test function."""
        if framework == 'pytest':
            return func_name.startswith('test_')
        elif framework == 'unittest':
            return func_name.startswith('test_')
        elif framework in ['jest', 'mocha']:
            return func_name in ['test', 'it']
        elif framework == 'go test':
            return func_name.startswith('Test')

        return func_name.startswith('test_')

    def _is_test_class(self, class_name: str, framework: str) -> bool:
        """Check if a class is a test class."""
        test_patterns = ['Test', 'TestCase', 'Spec']
        return any(pattern in class_name for pattern in test_patterns)

    def _is_test_method(self, method_name: str, framework: str) -> bool:
        """Check if a method is a test method."""
        return self._is_test_function(method_name, framework)

    def _create_test_case_from_function(self, func, parsed_file: ParsedFile, framework: str) -> Optional[TestCase]:
        """Create TestCase from a function."""
        test_type = self._determine_test_type(func.name, str(parsed_file.path))

        # Extract test details
        assertions = self._extract_assertions(func, framework)
        mocks = self._extract_mocks(func, framework)
        fixtures = self._extract_fixtures(func, framework)

        return TestCase(
            name=func.name,
            test_type=test_type,
            description=func.docstring,
            target_function=self._extract_target_function(func),
            target_class=None,
            framework=framework,
            file_path=parsed_file.path,
            line_number=func.line_number,
            assertions=assertions,
            setup_methods=[],
            teardown_methods=[],
            fixtures=fixtures,
            mocks=mocks,
            tags=self._extract_test_tags(func)
        )

    def _create_test_case_from_method(self, method, cls, parsed_file: ParsedFile, framework: str) -> Optional[TestCase]:
        """Create TestCase from a class method."""
        test_type = self._determine_test_type(method.name, str(parsed_file.path))

        # Extract test details
        assertions = self._extract_assertions(method, framework)
        mocks = self._extract_mocks(method, framework)
        fixtures = self._extract_fixtures(method, framework)

        return TestCase(
            name=f"{cls.name}.{method.name}",
            test_type=test_type,
            description=method.docstring,
            target_function=self._extract_target_function(method),
            target_class=self._extract_target_class(cls),
            framework=framework,
            file_path=parsed_file.path,
            line_number=method.line_number,
            assertions=assertions,
            setup_methods=self._extract_setup_methods(cls, framework),
            teardown_methods=self._extract_teardown_methods(cls, framework),
            fixtures=fixtures,
            mocks=mocks,
            tags=self._extract_test_tags(method)
        )

    def _determine_test_type(self, test_name: str, file_path: str) -> TestType:
        """Determine the type of test based on name and path."""
        test_name_lower = test_name.lower()
        file_path_lower = file_path.lower()

        for test_type, patterns in self.test_type_patterns.items():
            if any(pattern in test_name_lower or pattern in file_path_lower for pattern in patterns):
                return test_type

        return TestType.UNIT  # Default

    def _extract_assertions(self, func_or_method, framework: str) -> List[str]:
        """Extract assertion patterns from function/method."""
        assertions = []

        if framework in self.test_frameworks:
            framework_config = self.test_frameworks[framework]
            assertion_patterns = framework_config.get('assertions', [])

            # This would require reading the function body
            # For now, return empty list
            # In a full implementation, we'd parse the function body

        return assertions

    def _extract_mocks(self, func_or_method, framework: str) -> List[str]:
        """Extract mock patterns from function/method."""
        mocks = []

        if framework in self.test_frameworks:
            framework_config = self.test_frameworks[framework]
            mock_patterns = framework_config.get('mocks', [])

            # This would require reading the function body
            # For now, return empty list

        return mocks

    def _extract_fixtures(self, func_or_method, framework: str) -> List[str]:
        """Extract fixture usage from function/method."""
        fixtures = []

        # Look at function arguments for fixture names
        for arg in func_or_method.args:
            # In pytest, fixtures are passed as arguments
            if framework == 'pytest':
                arg_name = arg.split(':')[0] if ':' in arg else arg
                fixtures.append(arg_name)

        return fixtures

    def _extract_target_function(self, func_or_method) -> Optional[str]:
        """Extract the function being tested."""
        # Try to infer from test name
        test_name = func_or_method.name
        if test_name.startswith('test_'):
            return test_name[5:]  # Remove 'test_' prefix

        return None

    def _extract_target_class(self, cls) -> Optional[str]:
        """Extract the class being tested."""
        class_name = cls.name
        if class_name.startswith('Test'):
            return class_name[4:]  # Remove 'Test' prefix
        elif class_name.endswith('Test'):
            return class_name[:-4]  # Remove 'Test' suffix

        return None

    def _extract_setup_methods(self, cls, framework: str) -> List[str]:
        """Extract setup methods from test class."""
        setup_methods = []

        if framework in self.test_frameworks:
            framework_config = self.test_frameworks[framework]
            setup_patterns = framework_config.get('setup', [])

            for method in cls.methods:
                if any(pattern in method.name for pattern in setup_patterns):
                    setup_methods.append(method.name)

        return setup_methods

    def _extract_teardown_methods(self, cls, framework: str) -> List[str]:
        """Extract teardown methods from test class."""
        teardown_methods = []

        if framework in self.test_frameworks:
            framework_config = self.test_frameworks[framework]
            teardown_patterns = framework_config.get('teardown', [])

            for method in cls.methods:
                if any(pattern in method.name for pattern in teardown_patterns):
                    teardown_methods.append(method.name)

        return teardown_methods

    def _extract_test_tags(self, func_or_method) -> List[str]:
        """Extract test tags/markers from decorators."""
        tags = []

        for decorator in func_or_method.decorators:
            if 'mark.' in decorator:
                # pytest markers
                if 'mark.' in decorator:
                    tag = decorator.split('mark.')[1].split('(')[0]
                    tags.append(tag)

        return tags

    def _create_test_suite_from_file(self, test_file: ParsedFile, test_cases: List[TestCase]) -> Optional[TestSuite]:
        """Create TestSuite from a test file."""
        if not test_cases:
            return None

        framework = test_cases[0].framework if test_cases else 'unknown'

        # Look for test classes to group tests
        test_classes = [cls for cls in test_file.classes if self._is_test_class(cls.name, framework)]

        if test_classes:
            # Use first test class as suite
            cls = test_classes[0]
            return TestSuite(
                name=cls.name,
                test_cases=test_cases,
                setup_class=self._find_setup_class_method(cls),
                teardown_class=self._find_teardown_class_method(cls),
                shared_fixtures=[],
                file_path=test_file.path,
                framework=framework
            )
        else:
            # File-based suite
            return TestSuite(
                name=test_file.path.stem,
                test_cases=test_cases,
                setup_class=None,
                teardown_class=None,
                shared_fixtures=[],
                file_path=test_file.path,
                framework=framework
            )

    def _find_setup_class_method(self, cls) -> Optional[str]:
        """Find class-level setup method."""
        setup_names = ['setUpClass', 'setup_class', 'beforeAll']
        for method in cls.methods:
            if method.name in setup_names:
                return method.name
        return None

    def _find_teardown_class_method(self, cls) -> Optional[str]:
        """Find class-level teardown method."""
        teardown_names = ['tearDownClass', 'teardown_class', 'afterAll']
        for method in cls.methods:
            if method.name in teardown_names:
                return method.name
        return None

    def _calculate_coverage(self, test_cases: List[TestCase], all_files: List[ParsedFile]) -> TestCoverage:
        """Calculate test coverage based on test cases and source files."""
        covered_functions = set()
        covered_classes = set()

        # Extract covered items from test cases
        for test_case in test_cases:
            if test_case.target_function:
                covered_functions.add(test_case.target_function)
            if test_case.target_class:
                covered_classes.add(test_case.target_class)

        # Get all functions and classes from source files (non-test files)
        all_functions = set()
        all_classes = set()

        for parsed_file in all_files:
            if not self._is_test_file(parsed_file):
                for func in parsed_file.functions:
                    if func.is_public:
                        all_functions.add(func.name)
                for cls in parsed_file.classes:
                    if cls.is_public:
                        all_classes.add(cls.name)

        uncovered_functions = all_functions - covered_functions
        uncovered_classes = all_classes - covered_classes

        # Calculate coverage percentage
        total_items = len(all_functions) + len(all_classes)
        covered_items = len(covered_functions) + len(covered_classes)
        coverage_percentage = (covered_items / total_items * 100) if total_items > 0 else 0.0

        return TestCoverage(
            covered_functions=covered_functions,
            covered_classes=covered_classes,
            uncovered_functions=uncovered_functions,
            uncovered_classes=uncovered_classes,
            coverage_percentage=coverage_percentage
        )

    def _find_test_config_files(self, parsed_files: List[ParsedFile]) -> List[Path]:
        """Find test configuration files."""
        config_files = []

        config_patterns = [
            'pytest.ini', 'pyproject.toml', 'setup.cfg',
            'jest.config.js', 'jest.config.json',
            'karma.conf.js', 'mocha.opts',
            'phpunit.xml', 'go.mod'
        ]

        for parsed_file in parsed_files:
            filename = parsed_file.path.name
            if any(pattern in filename for pattern in config_patterns):
                config_files.append(parsed_file.path)

        return config_files

    def _identify_test_runners(self, parsed_files: List[ParsedFile]) -> List[str]:
        """Identify test runners used in the project."""
        runners = set()

        # Check for package.json scripts
        for parsed_file in parsed_files:
            if parsed_file.path.name == 'package.json':
                try:
                    with open(parsed_file.path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    if 'jest' in content:
                        runners.add('jest')
                    if 'mocha' in content:
                        runners.add('mocha')
                    if 'karma' in content:
                        runners.add('karma')

                except Exception:
                    pass

        # Check for Python test runners
        for parsed_file in parsed_files:
            for imp in parsed_file.imports:
                if 'pytest' in imp.module:
                    runners.add('pytest')
                elif 'unittest' in imp.module:
                    runners.add('unittest')

        return list(runners)