"""API endpoint extractor for identifying REST APIs, GraphQL, and other API patterns."""

from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from ..parsers.base_parser import ParsedFile


@dataclass
class APIEndpoint:
    """Represents an API endpoint."""
    path: str
    method: str
    function_name: str
    parameters: List[str]
    return_type: Optional[str]
    description: Optional[str]
    framework: str
    file_path: Path
    line_number: int
    middleware: List[str]
    authentication: Optional[str]
    tags: List[str]


@dataclass
class APISchema:
    """Represents an API schema or documentation."""
    name: str
    version: Optional[str]
    base_url: Optional[str]
    endpoints: List[APIEndpoint]
    models: List[str]
    authentication_schemes: List[str]
    documentation_url: Optional[str]


class APIExtractor:
    """Extracts API endpoints and patterns from parsed code files."""

    def __init__(self):
        self.framework_patterns = {
            'fastapi': {
                'decorators': ['@app.get', '@app.post', '@app.put', '@app.delete', '@app.patch'],
                'router_patterns': ['@router.get', '@router.post', '@router.put', '@router.delete'],
                'dependencies': ['fastapi', 'FastAPI']
            },
            'flask': {
                'decorators': ['@app.route', '@bp.route', '@blueprint.route'],
                'methods': ['methods=', 'GET', 'POST', 'PUT', 'DELETE'],
                'dependencies': ['flask', 'Flask']
            },
            'django': {
                'patterns': ['path(', 'url(', 'urlpatterns'],
                'views': ['APIView', 'ViewSet', 'GenericAPIView'],
                'dependencies': ['django', 'rest_framework']
            },
            'express': {
                'patterns': ['app.get', 'app.post', 'app.put', 'app.delete', 'router.'],
                'dependencies': ['express']
            },
            'nestjs': {
                'decorators': ['@Get', '@Post', '@Put', '@Delete', '@Patch'],
                'dependencies': ['@nestjs/common']
            },
            'gin': {
                'patterns': ['gin.Engine', 'router.GET', 'router.POST'],
                'dependencies': ['github.com/gin-gonic/gin']
            },
            'echo': {
                'patterns': ['echo.New', 'e.GET', 'e.POST'],
                'dependencies': ['github.com/labstack/echo']
            }
        }

    def extract_from_files(self, parsed_files: List[ParsedFile]) -> APISchema:
        """Extract API information from multiple parsed files."""
        all_endpoints = []
        detected_frameworks = set()
        models = set()
        auth_schemes = set()

        for parsed_file in parsed_files:
            endpoints = self._extract_endpoints_from_file(parsed_file)
            all_endpoints.extend(endpoints)

            # Detect frameworks from this file
            framework = self._detect_framework(parsed_file)
            if framework:
                detected_frameworks.add(framework)

            # Extract model references
            file_models = self._extract_model_references(parsed_file)
            models.update(file_models)

            # Extract authentication patterns
            auth = self._extract_auth_patterns(parsed_file)
            if auth:
                auth_schemes.update(auth)

        # Determine primary framework
        primary_framework = self._determine_primary_framework(detected_frameworks)

        # Extract API metadata
        base_url = self._extract_base_url(parsed_files)
        version = self._extract_api_version(parsed_files)

        return APISchema(
            name=self._generate_api_name(parsed_files),
            version=version,
            base_url=base_url,
            endpoints=all_endpoints,
            models=list(models),
            authentication_schemes=list(auth_schemes),
            documentation_url=self._find_documentation_url(parsed_files)
        )

    def _extract_endpoints_from_file(self, parsed_file: ParsedFile) -> List[APIEndpoint]:
        """Extract API endpoints from a single parsed file."""
        endpoints = []

        # Use parser-specific endpoint extraction
        if hasattr(parsed_file, 'path'):
            # Get parser instance based on file type
            from ..parsers.python_parser import PythonParser
            from ..parsers.javascript_parser import JavaScriptParser
            from ..parsers.typescript_parser import TypeScriptParser
            from ..parsers.go_parser import GoParser

            parser = None
            if parsed_file.path.suffix == '.py':
                parser = PythonParser()
            elif parsed_file.path.suffix in ['.js', '.jsx']:
                parser = JavaScriptParser()
            elif parsed_file.path.suffix in ['.ts', '.tsx']:
                parser = TypeScriptParser()
            elif parsed_file.path.suffix == '.go':
                parser = GoParser()

            if parser:
                raw_endpoints = parser.extract_api_endpoints(parsed_file)
                for endpoint_data in raw_endpoints:
                    endpoint = self._create_endpoint_from_data(endpoint_data, parsed_file)
                    if endpoint:
                        endpoints.append(endpoint)

        return endpoints

    def _create_endpoint_from_data(self, data: Dict[str, Any], parsed_file: ParsedFile) -> Optional[APIEndpoint]:
        """Create APIEndpoint from raw endpoint data."""
        try:
            return APIEndpoint(
                path=data.get('path', '/'),
                method=data.get('method', 'GET'),
                function_name=data.get('function', 'unknown'),
                parameters=data.get('args', []),
                return_type=data.get('return_type'),
                description=data.get('docstring'),
                framework=data.get('framework', 'unknown'),
                file_path=parsed_file.path,
                line_number=data.get('line_number', 0),
                middleware=self._extract_middleware(data),
                authentication=self._extract_auth_from_endpoint(data),
                tags=self._extract_tags(data)
            )
        except Exception:
            return None

    def _detect_framework(self, parsed_file: ParsedFile) -> Optional[str]:
        """Detect which API framework is being used."""
        # Check imports
        import_modules = [imp.module for imp in parsed_file.imports]
        import_names = [name for imp in parsed_file.imports for name in imp.names]

        for framework, patterns in self.framework_patterns.items():
            dependencies = patterns.get('dependencies', [])
            if any(dep in module for module in import_modules for dep in dependencies):
                return framework
            if any(dep in import_names for dep in dependencies):
                return framework

        # Check code patterns by reading file content
        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            for framework, patterns in self.framework_patterns.items():
                # Check decorators
                decorators = patterns.get('decorators', [])
                if any(dec in content for dec in decorators):
                    return framework

                # Check patterns
                code_patterns = patterns.get('patterns', [])
                if any(pattern in content for pattern in code_patterns):
                    return framework

        except Exception:
            pass

        return None

    def _extract_model_references(self, parsed_file: ParsedFile) -> List[str]:
        """Extract data model references from API file."""
        models = []

        # Look for Pydantic models, Django models, etc.
        for cls in parsed_file.classes:
            if any(base in ['BaseModel', 'Model', 'Serializer'] for base in cls.base_classes):
                models.append(cls.name)

        # Look for type annotations in function signatures
        for func in parsed_file.functions:
            if func.return_type:
                # Extract model names from return type annotations
                models.extend(self._extract_types_from_annotation(func.return_type))

            for arg in func.args:
                if ':' in arg:
                    type_part = arg.split(':', 1)[1].strip()
                    models.extend(self._extract_types_from_annotation(type_part))

        return list(set(models))

    def _extract_auth_patterns(self, parsed_file: ParsedFile) -> List[str]:
        """Extract authentication patterns from file."""
        auth_patterns = []

        # Check for authentication-related imports
        auth_imports = [
            'jwt', 'oauth', 'auth', 'authentication', 'authorization',
            'token', 'session', 'passport', 'security'
        ]

        for imp in parsed_file.imports:
            if any(auth_term in imp.module.lower() for auth_term in auth_imports):
                if 'jwt' in imp.module.lower():
                    auth_patterns.append('JWT')
                elif 'oauth' in imp.module.lower():
                    auth_patterns.append('OAuth')
                elif 'session' in imp.module.lower():
                    auth_patterns.append('Session')
                else:
                    auth_patterns.append('Token')

        # Check for authentication decorators
        for func in parsed_file.functions:
            for decorator in func.decorators:
                if any(auth_term in decorator.lower() for auth_term in auth_imports):
                    auth_patterns.append('Decorator-based')

        return list(set(auth_patterns))

    def _extract_middleware(self, endpoint_data: Dict[str, Any]) -> List[str]:
        """Extract middleware from endpoint data."""
        # This would need framework-specific logic
        return []

    def _extract_auth_from_endpoint(self, endpoint_data: Dict[str, Any]) -> Optional[str]:
        """Extract authentication requirement from endpoint."""
        # Look for auth-related decorators or patterns
        return None

    def _extract_tags(self, endpoint_data: Dict[str, Any]) -> List[str]:
        """Extract tags/categories from endpoint."""
        tags = []

        # Infer tags from path
        path = endpoint_data.get('path', '')
        if path.startswith('/api/'):
            path_parts = path.split('/')[2:]  # Remove /api/
            if path_parts:
                tags.append(path_parts[0])

        return tags

    def _extract_types_from_annotation(self, annotation: str) -> List[str]:
        """Extract type names from type annotation string."""
        # Simple extraction - could be improved with proper parsing
        import re

        # Match capitalized words (likely type names)
        types = re.findall(r'[A-Z][a-zA-Z0-9_]*', annotation)

        # Filter out common built-in types
        builtin_types = {'List', 'Dict', 'Optional', 'Union', 'Any', 'Callable'}
        return [t for t in types if t not in builtin_types]

    def _determine_primary_framework(self, frameworks: set) -> Optional[str]:
        """Determine the primary framework from detected frameworks."""
        if not frameworks:
            return None

        # Prioritize by popularity/likelihood
        priority = ['fastapi', 'flask', 'django', 'express', 'nestjs', 'gin', 'echo']

        for framework in priority:
            if framework in frameworks:
                return framework

        return list(frameworks)[0]

    def _extract_base_url(self, parsed_files: List[ParsedFile]) -> Optional[str]:
        """Extract base URL from configuration files."""
        # Look for base URL in config files
        for parsed_file in parsed_files:
            if 'config' in str(parsed_file.path).lower():
                try:
                    with open(parsed_file.path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Look for URL patterns
                    import re
                    url_patterns = [
                        r'BASE_URL\s*=\s*["\']([^"\']+)["\']',
                        r'API_URL\s*=\s*["\']([^"\']+)["\']',
                        r'host\s*:\s*["\']([^"\']+)["\']',
                    ]

                    for pattern in url_patterns:
                        match = re.search(pattern, content)
                        if match:
                            return match.group(1)

                except Exception:
                    continue

        return None

    def _extract_api_version(self, parsed_files: List[ParsedFile]) -> Optional[str]:
        """Extract API version from files."""
        # Look for version in various places
        for parsed_file in parsed_files:
            try:
                with open(parsed_file.path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Look for version patterns
                import re
                version_patterns = [
                    r'version\s*=\s*["\']([^"\']+)["\']',
                    r'API_VERSION\s*=\s*["\']([^"\']+)["\']',
                    r'"version"\s*:\s*"([^"]+)"',
                ]

                for pattern in version_patterns:
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        return match.group(1)

            except Exception:
                continue

        return None

    def _generate_api_name(self, parsed_files: List[ParsedFile]) -> str:
        """Generate API name from project structure."""
        # Use directory name or package name
        if parsed_files:
            first_file = parsed_files[0]
            project_name = first_file.path.parent.name
            return f"{project_name.title()} API"

        return "Unknown API"

    def _find_documentation_url(self, parsed_files: List[ParsedFile]) -> Optional[str]:
        """Find API documentation URL."""
        # Look for common documentation patterns
        for parsed_file in parsed_files:
            try:
                with open(parsed_file.path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Look for Swagger/OpenAPI docs
                if any(pattern in content.lower() for pattern in ['swagger', 'openapi', '/docs']):
                    return '/docs'

            except Exception:
                continue

        return None