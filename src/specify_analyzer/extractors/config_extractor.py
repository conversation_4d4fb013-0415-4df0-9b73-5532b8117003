"""Configuration extractor for identifying project settings, environment variables, and deployment configs."""

from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass
from enum import Enum
import json
import re

from ..parsers.base_parser import ParsedFile


class ConfigType(Enum):
    """Types of configuration files."""
    ENVIRONMENT = "environment"
    DATABASE = "database"
    API = "api"
    BUILD = "build"
    DEPLOYMENT = "deployment"
    TESTING = "testing"
    LOGGING = "logging"
    SECURITY = "security"
    GENERAL = "general"


@dataclass
class ConfigEntry:
    """Represents a single configuration entry."""
    key: str
    value: Optional[str]
    data_type: str
    description: Optional[str]
    required: bool
    default_value: Optional[str]
    environment_variable: Optional[str]
    source_file: Path
    line_number: Optional[int]


@dataclass
class ConfigSection:
    """Represents a section of configuration."""
    name: str
    config_type: ConfigType
    entries: List[ConfigEntry]
    description: Optional[str]
    source_file: Path


@dataclass
class DeploymentTarget:
    """Represents a deployment target/environment."""
    name: str
    type: str  # docker, kubernetes, serverless, etc.
    config_files: List[Path]
    environment_variables: Dict[str, str]
    services: List[str]
    dependencies: List[str]


@dataclass
class ConfigSchema:
    """Represents the complete configuration schema."""
    sections: List[ConfigSection]
    environment_variables: Dict[str, ConfigEntry]
    deployment_targets: List[DeploymentTarget]
    config_files: List[Path]
    secrets: List[str]
    external_services: Dict[str, Dict[str, Any]]
    build_tools: Set[str]


class ConfigExtractor:
    """Extracts configuration information from project files."""

    def __init__(self):
        self.config_file_patterns = {
            'environment': ['.env', '.env.local', '.env.prod', '.env.dev', '.environment'],
            'docker': ['Dockerfile', 'docker-compose.yml', '.dockerignore'],
            'kubernetes': ['*.yaml', '*.yml', 'kustomization.yaml'],
            'package_managers': [
                'package.json', 'pyproject.toml', 'requirements.txt', 'Cargo.toml',
                'go.mod', 'pom.xml', 'build.gradle', 'composer.json'
            ],
            'build_tools': [
                'webpack.config.js', 'vite.config.js', 'rollup.config.js',
                'tsconfig.json', 'babel.config.js', 'Makefile', 'CMakeLists.txt'
            ],
            'testing': [
                'pytest.ini', 'jest.config.js', 'karma.conf.js',
                'phpunit.xml', 'testng.xml'
            ],
            'ci_cd': [
                '.github/workflows/', '.gitlab-ci.yml', 'azure-pipelines.yml',
                'Jenkinsfile', '.travis.yml', 'circle.yml'
            ],
            'web_servers': [
                'nginx.conf', 'apache.conf', '.htaccess', 'httpd.conf'
            ]
        }

        self.environment_patterns = {
            'database': ['DB_', 'DATABASE_', 'POSTGRES_', 'MYSQL_', 'MONGO_'],
            'api': ['API_', 'REST_', 'GRAPHQL_', 'ENDPOINT_'],
            'security': ['SECRET_', 'KEY_', 'TOKEN_', 'AUTH_', 'JWT_'],
            'logging': ['LOG_', 'LOGGER_', 'SENTRY_'],
            'cache': ['CACHE_', 'REDIS_', 'MEMCACHED_'],
            'external': ['AWS_', 'AZURE_', 'GCP_', 'STRIPE_', 'SENDGRID_']
        }

    def extract_from_files(self, parsed_files: List[ParsedFile]) -> ConfigSchema:
        """Extract configuration schema from multiple parsed files."""
        sections = []
        environment_variables = {}
        deployment_targets = []
        config_files = []
        secrets = []
        external_services = {}
        build_tools = set()

        # Categorize files
        for parsed_file in parsed_files:
            if self._is_config_file(parsed_file.path):
                config_files.append(parsed_file.path)

                # Extract configuration from file
                file_sections = self._extract_config_from_file(parsed_file)
                sections.extend(file_sections)

                # Extract environment variables
                env_vars = self._extract_environment_variables(parsed_file)
                environment_variables.update(env_vars)

                # Extract secrets
                file_secrets = self._extract_secrets(parsed_file)
                secrets.extend(file_secrets)

                # Extract external services
                services = self._extract_external_services(parsed_file)
                external_services.update(services)

                # Identify build tools
                tools = self._identify_build_tools(parsed_file)
                build_tools.update(tools)

        # Extract deployment targets
        deployment_targets = self._extract_deployment_targets(parsed_files)

        return ConfigSchema(
            sections=sections,
            environment_variables=environment_variables,
            deployment_targets=deployment_targets,
            config_files=config_files,
            secrets=list(set(secrets)),
            external_services=external_services,
            build_tools=build_tools
        )

    def _is_config_file(self, file_path: Path) -> bool:
        """Check if a file is a configuration file."""
        filename = file_path.name.lower()
        path_str = str(file_path).lower()

        # Check specific filenames
        config_names = [
            '.env', 'config', 'settings', 'docker', 'compose',
            'package.json', 'pyproject.toml', 'requirements', 'cargo.toml',
            'makefile', 'webpack', 'babel', 'jest', 'pytest'
        ]

        if any(name in filename for name in config_names):
            return True

        # Check file extensions
        config_extensions = ['.yml', '.yaml', '.json', '.toml', '.ini', '.conf', '.config']
        if file_path.suffix.lower() in config_extensions:
            return True

        # Check path patterns
        config_paths = ['config/', 'configs/', '.github/', 'deploy/', 'k8s/']
        if any(path in path_str for path in config_paths):
            return True

        return False

    def _extract_config_from_file(self, parsed_file: ParsedFile) -> List[ConfigSection]:
        """Extract configuration sections from a file."""
        sections = []

        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            filename = parsed_file.path.name.lower()

            if filename.endswith('.json'):
                sections.extend(self._parse_json_config(content, parsed_file.path))
            elif filename.endswith(('.yml', '.yaml')):
                sections.extend(self._parse_yaml_config(content, parsed_file.path))
            elif filename.endswith('.toml'):
                sections.extend(self._parse_toml_config(content, parsed_file.path))
            elif filename.endswith(('.ini', '.conf')):
                sections.extend(self._parse_ini_config(content, parsed_file.path))
            elif filename.startswith('.env'):
                sections.extend(self._parse_env_config(content, parsed_file.path))
            elif 'dockerfile' in filename:
                sections.extend(self._parse_dockerfile_config(content, parsed_file.path))
            else:
                # Try to extract key-value pairs generically
                sections.extend(self._parse_generic_config(content, parsed_file.path))

        except Exception:
            pass

        return sections

    def _parse_json_config(self, content: str, file_path: Path) -> List[ConfigSection]:
        """Parse JSON configuration file."""
        sections = []

        try:
            data = json.loads(content)

            if isinstance(data, dict):
                for section_name, section_data in data.items():
                    config_type = self._determine_config_type(section_name, file_path)
                    entries = self._extract_entries_from_dict(section_data, file_path)

                    sections.append(ConfigSection(
                        name=section_name,
                        config_type=config_type,
                        entries=entries,
                        description=None,
                        source_file=file_path
                    ))

        except json.JSONDecodeError:
            pass

        return sections

    def _parse_yaml_config(self, content: str, file_path: Path) -> List[ConfigSection]:
        """Parse YAML configuration file."""
        sections = []

        # Simple YAML parsing without external dependencies
        try:
            # Basic YAML parsing - in a real implementation, use PyYAML
            lines = content.split('\n')
            current_section = None
            entries = []

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                if ':' in line and not line.startswith(' '):
                    # New section
                    if current_section and entries:
                        config_type = self._determine_config_type(current_section, file_path)
                        sections.append(ConfigSection(
                            name=current_section,
                            config_type=config_type,
                            entries=entries,
                            description=None,
                            source_file=file_path
                        ))

                    current_section = line.split(':')[0].strip()
                    entries = []

                elif ':' in line and current_section:
                    # Configuration entry
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip() if value.strip() else None

                    entries.append(ConfigEntry(
                        key=key,
                        value=value,
                        data_type=self._infer_data_type(value),
                        description=None,
                        required=False,
                        default_value=value,
                        environment_variable=None,
                        source_file=file_path,
                        line_number=line_num
                    ))

            # Add last section
            if current_section and entries:
                config_type = self._determine_config_type(current_section, file_path)
                sections.append(ConfigSection(
                    name=current_section,
                    config_type=config_type,
                    entries=entries,
                    description=None,
                    source_file=file_path
                ))

        except Exception:
            pass

        return sections

    def _parse_toml_config(self, content: str, file_path: Path) -> List[ConfigSection]:
        """Parse TOML configuration file."""
        sections = []

        try:
            # Basic TOML parsing - in a real implementation, use tomllib
            lines = content.split('\n')
            current_section = 'general'
            entries = []

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                if line.startswith('[') and line.endswith(']'):
                    # New section
                    if entries:
                        config_type = self._determine_config_type(current_section, file_path)
                        sections.append(ConfigSection(
                            name=current_section,
                            config_type=config_type,
                            entries=entries,
                            description=None,
                            source_file=file_path
                        ))

                    current_section = line[1:-1]
                    entries = []

                elif '=' in line:
                    # Configuration entry
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"\'')

                    entries.append(ConfigEntry(
                        key=key,
                        value=value,
                        data_type=self._infer_data_type(value),
                        description=None,
                        required=False,
                        default_value=value,
                        environment_variable=None,
                        source_file=file_path,
                        line_number=line_num
                    ))

            # Add last section
            if entries:
                config_type = self._determine_config_type(current_section, file_path)
                sections.append(ConfigSection(
                    name=current_section,
                    config_type=config_type,
                    entries=entries,
                    description=None,
                    source_file=file_path
                ))

        except Exception:
            pass

        return sections

    def _parse_ini_config(self, content: str, file_path: Path) -> List[ConfigSection]:
        """Parse INI configuration file."""
        return self._parse_toml_config(content, file_path)  # Similar format

    def _parse_env_config(self, content: str, file_path: Path) -> List[ConfigSection]:
        """Parse environment variable file."""
        entries = []

        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')

                entries.append(ConfigEntry(
                    key=key,
                    value=value,
                    data_type=self._infer_data_type(value),
                    description=None,
                    required=True,
                    default_value=None,
                    environment_variable=key,
                    source_file=file_path,
                    line_number=line_num
                ))

        if entries:
            return [ConfigSection(
                name='environment',
                config_type=ConfigType.ENVIRONMENT,
                entries=entries,
                description='Environment variables',
                source_file=file_path
            )]

        return []

    def _parse_dockerfile_config(self, content: str, file_path: Path) -> List[ConfigSection]:
        """Parse Dockerfile for configuration."""
        entries = []

        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('ENV '):
                # Extract environment variables
                env_part = line[4:].strip()
                if '=' in env_part:
                    key, value = env_part.split('=', 1)
                    entries.append(ConfigEntry(
                        key=key.strip(),
                        value=value.strip(),
                        data_type='string',
                        description=None,
                        required=False,
                        default_value=value.strip(),
                        environment_variable=key.strip(),
                        source_file=file_path,
                        line_number=line_num
                    ))

        if entries:
            return [ConfigSection(
                name='docker_environment',
                config_type=ConfigType.DEPLOYMENT,
                entries=entries,
                description='Docker environment configuration',
                source_file=file_path
            )]

        return []

    def _parse_generic_config(self, content: str, file_path: Path) -> List[ConfigSection]:
        """Parse generic configuration file."""
        entries = []

        # Look for key-value patterns
        patterns = [
            r'(\w+)\s*=\s*([^;\n]+)',  # key = value
            r'(\w+):\s*([^;\n]+)',     # key: value
            r'set\s+(\w+)\s+([^;\n]+)', # set key value
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                key = match.group(1).strip()
                value = match.group(2).strip().strip('"\'')

                entries.append(ConfigEntry(
                    key=key,
                    value=value,
                    data_type=self._infer_data_type(value),
                    description=None,
                    required=False,
                    default_value=value,
                    environment_variable=None,
                    source_file=file_path,
                    line_number=content[:match.start()].count('\n') + 1
                ))

        if entries:
            return [ConfigSection(
                name=file_path.stem,
                config_type=self._determine_config_type(file_path.name, file_path),
                entries=entries,
                description=None,
                source_file=file_path
            )]

        return []

    def _extract_entries_from_dict(self, data: Any, file_path: Path, prefix: str = '') -> List[ConfigEntry]:
        """Extract configuration entries from dictionary data."""
        entries = []

        if isinstance(data, dict):
            for key, value in data.items():
                full_key = f"{prefix}.{key}" if prefix else key

                if isinstance(value, dict):
                    # Nested configuration
                    entries.extend(self._extract_entries_from_dict(value, file_path, full_key))
                else:
                    entries.append(ConfigEntry(
                        key=full_key,
                        value=str(value) if value is not None else None,
                        data_type=self._infer_data_type(value),
                        description=None,
                        required=False,
                        default_value=str(value) if value is not None else None,
                        environment_variable=None,
                        source_file=file_path,
                        line_number=None
                    ))

        return entries

    def _determine_config_type(self, section_name: str, file_path: Path) -> ConfigType:
        """Determine the type of configuration section."""
        section_lower = section_name.lower()
        filename_lower = file_path.name.lower()

        if any(term in section_lower for term in ['env', 'environment']):
            return ConfigType.ENVIRONMENT
        elif any(term in section_lower for term in ['db', 'database', 'postgres', 'mysql', 'mongo']):
            return ConfigType.DATABASE
        elif any(term in section_lower for term in ['api', 'rest', 'graphql', 'endpoint']):
            return ConfigType.API
        elif any(term in section_lower for term in ['build', 'webpack', 'rollup', 'vite']):
            return ConfigType.BUILD
        elif any(term in section_lower for term in ['deploy', 'docker', 'k8s', 'kubernetes']):
            return ConfigType.DEPLOYMENT
        elif any(term in section_lower for term in ['test', 'jest', 'pytest', 'mocha']):
            return ConfigType.TESTING
        elif any(term in section_lower for term in ['log', 'logger', 'sentry']):
            return ConfigType.LOGGING
        elif any(term in section_lower for term in ['auth', 'security', 'jwt', 'oauth']):
            return ConfigType.SECURITY
        elif 'docker' in filename_lower or 'compose' in filename_lower:
            return ConfigType.DEPLOYMENT
        else:
            return ConfigType.GENERAL

    def _infer_data_type(self, value: Any) -> str:
        """Infer data type from value."""
        if value is None:
            return 'null'
        elif isinstance(value, bool):
            return 'boolean'
        elif isinstance(value, int):
            return 'integer'
        elif isinstance(value, float):
            return 'float'
        elif isinstance(value, list):
            return 'array'
        elif isinstance(value, dict):
            return 'object'
        elif isinstance(value, str):
            # Try to infer more specific types
            if value.lower() in ['true', 'false']:
                return 'boolean'
            elif value.isdigit():
                return 'integer'
            elif re.match(r'^\d+\.\d+$', value):
                return 'float'
            elif '@' in value and '.' in value:
                return 'email'
            elif value.startswith(('http://', 'https://')):
                return 'url'
            else:
                return 'string'
        else:
            return 'unknown'

    def _extract_environment_variables(self, parsed_file: ParsedFile) -> Dict[str, ConfigEntry]:
        """Extract environment variables from file."""
        env_vars = {}

        # Look for environment variable usage in code
        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Common patterns for environment variables
            patterns = [
                r'os\.environ\.get\([\'"]([^\'"]+)[\'"]',  # Python: os.environ.get('VAR')
                r'process\.env\.([A-Z_]+)',                # JavaScript: process.env.VAR
                r'os\.Getenv\([\'"]([^\'"]+)[\'"]',        # Go: os.Getenv("VAR")
                r'\$\{([A-Z_]+)\}',                        # Shell: ${VAR}
                r'\$([A-Z_]+)',                            # Shell: $VAR
            ]

            for pattern in patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    var_name = match.group(1)
                    env_vars[var_name] = ConfigEntry(
                        key=var_name,
                        value=None,
                        data_type='string',
                        description=None,
                        required=True,
                        default_value=None,
                        environment_variable=var_name,
                        source_file=parsed_file.path,
                        line_number=content[:match.start()].count('\n') + 1
                    )

        except Exception:
            pass

        return env_vars

    def _extract_secrets(self, parsed_file: ParsedFile) -> List[str]:
        """Extract secret keys and sensitive configuration."""
        secrets = []

        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Look for secret-like keys
            secret_patterns = [
                'password', 'secret', 'key', 'token', 'api_key',
                'private_key', 'auth', 'credential', 'jwt'
            ]

            for line in content.split('\n'):
                line_lower = line.lower()
                if any(pattern in line_lower for pattern in secret_patterns):
                    if '=' in line or ':' in line:
                        # Extract the key name
                        if '=' in line:
                            key = line.split('=')[0].strip()
                        else:
                            key = line.split(':')[0].strip()

                        secrets.append(key)

        except Exception:
            pass

        return secrets

    def _extract_external_services(self, parsed_file: ParsedFile) -> Dict[str, Dict[str, Any]]:
        """Extract external service configurations."""
        services = {}

        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Common external services
            service_patterns = {
                'aws': ['aws', 'amazon', 's3', 'ec2', 'lambda'],
                'azure': ['azure', 'microsoft'],
                'gcp': ['google', 'gcp', 'cloud'],
                'stripe': ['stripe'],
                'sendgrid': ['sendgrid'],
                'twilio': ['twilio'],
                'redis': ['redis'],
                'mongodb': ['mongo', 'mongodb'],
                'postgresql': ['postgres', 'postgresql'],
                'mysql': ['mysql'],
            }

            for service, keywords in service_patterns.items():
                if any(keyword in content.lower() for keyword in keywords):
                    services[service] = {
                        'detected': True,
                        'keywords_found': [kw for kw in keywords if kw in content.lower()]
                    }

        except Exception:
            pass

        return services

    def _identify_build_tools(self, parsed_file: ParsedFile) -> Set[str]:
        """Identify build tools from configuration files."""
        build_tools = set()

        filename = parsed_file.path.name.lower()
        path_str = str(parsed_file.path).lower()

        tool_patterns = {
            'webpack': ['webpack.config', 'webpack'],
            'vite': ['vite.config', 'vite'],
            'rollup': ['rollup.config', 'rollup'],
            'parcel': ['parcel', '.parcelrc'],
            'gulp': ['gulpfile', 'gulp'],
            'grunt': ['gruntfile', 'grunt'],
            'make': ['makefile', 'make'],
            'cmake': ['cmakelists.txt', 'cmake'],
            'gradle': ['build.gradle', 'gradle'],
            'maven': ['pom.xml', 'maven'],
            'npm': ['package.json'],
            'yarn': ['yarn.lock'],
            'docker': ['dockerfile', 'docker'],
        }

        for tool, patterns in tool_patterns.items():
            if any(pattern in filename or pattern in path_str for pattern in patterns):
                build_tools.add(tool)

        return build_tools

    def _extract_deployment_targets(self, parsed_files: List[ParsedFile]) -> List[DeploymentTarget]:
        """Extract deployment target configurations."""
        targets = []

        # Look for Docker configurations
        docker_files = [f for f in parsed_files if 'docker' in str(f.path).lower()]
        if docker_files:
            targets.append(DeploymentTarget(
                name='docker',
                type='container',
                config_files=[f.path for f in docker_files],
                environment_variables={},
                services=[],
                dependencies=[]
            ))

        # Look for Kubernetes configurations
        k8s_files = [f for f in parsed_files if any(ext in str(f.path).lower()
                    for ext in ['k8s', 'kubernetes', 'kustomization'])]
        if k8s_files:
            targets.append(DeploymentTarget(
                name='kubernetes',
                type='orchestration',
                config_files=[f.path for f in k8s_files],
                environment_variables={},
                services=[],
                dependencies=[]
            ))

        # Look for CI/CD configurations
        ci_files = [f for f in parsed_files if any(pattern in str(f.path).lower()
                   for pattern in ['.github/workflows', '.gitlab-ci', 'azure-pipelines'])]
        if ci_files:
            targets.append(DeploymentTarget(
                name='ci_cd',
                type='automation',
                config_files=[f.path for f in ci_files],
                environment_variables={},
                services=[],
                dependencies=[]
            ))

        return targets