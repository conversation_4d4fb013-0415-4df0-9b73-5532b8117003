"""Pattern inference engine for extracting high-level requirements and architectural patterns from code analysis."""

from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from .scanner import ScanResult
from .extractors.api_extractor import APISchema
from .extractors.model_extractor import ModelSchema
from .extractors.test_extractor import TestSchema
from .extractors.config_extractor import ConfigSchema


class ProjectType(Enum):
    """High-level project types."""
    WEB_APPLICATION = "web_application"
    REST_API = "rest_api"
    MICROSERVICE = "microservice"
    CLI_TOOL = "cli_tool"
    LIBRARY = "library"
    MOBILE_APP = "mobile_app"
    DESKTOP_APP = "desktop_app"
    DATA_PIPELINE = "data_pipeline"
    ML_MODEL = "ml_model"
    GAME = "game"
    UNKNOWN = "unknown"


class Architecture(Enum):
    """Architectural patterns."""
    MONOLITHIC = "monolithic"
    MICROSERVICES = "microservices"
    LAYERED = "layered"
    MVC = "mvc"
    MVP = "mvp"
    MVVM = "mvvm"
    HEXAGONAL = "hexagonal"
    EVENT_DRIVEN = "event_driven"
    SERVERLESS = "serverless"
    UNKNOWN = "unknown"


@dataclass
class UserStory:
    """Represents an inferred user story."""
    title: str
    description: str
    acceptance_criteria: List[str]
    priority: str
    estimated_effort: str
    related_endpoints: List[str]
    related_models: List[str]


@dataclass
class FunctionalRequirement:
    """Represents a functional requirement."""
    id: str
    title: str
    description: str
    user_stories: List[UserStory]
    acceptance_criteria: List[str]
    dependencies: List[str]
    api_endpoints: List[str]
    data_models: List[str]


@dataclass
class NonFunctionalRequirement:
    """Represents a non-functional requirement."""
    category: str  # performance, security, scalability, etc.
    requirement: str
    description: str
    metrics: List[str]
    constraints: List[str]
    evidence: List[str]  # What in the code suggests this requirement


@dataclass
class TechnicalDecision:
    """Represents a technical decision made in the project."""
    title: str
    description: str
    alternatives_considered: List[str]
    rationale: str
    consequences: List[str]
    evidence: List[str]


@dataclass
class InferenceResult:
    """Complete inference results from code analysis."""
    project_type: ProjectType
    architecture: Architecture
    primary_purpose: str
    target_users: List[str]
    functional_requirements: List[FunctionalRequirement]
    non_functional_requirements: List[NonFunctionalRequirement]
    user_stories: List[UserStory]
    technical_decisions: List[TechnicalDecision]
    key_entities: List[str]
    integrations: List[str]
    deployment_targets: List[str]
    performance_requirements: List[str]
    security_considerations: List[str]


class PatternInference:
    """Infers high-level patterns and requirements from extracted code information."""

    def __init__(self):
        self.project_type_indicators = {
            ProjectType.WEB_APPLICATION: {
                'frameworks': ['react', 'vue', 'angular', 'svelte'],
                'patterns': ['frontend', 'backend', 'html', 'css'],
                'files': ['index.html', 'app.js', 'main.css']
            },
            ProjectType.REST_API: {
                'frameworks': ['fastapi', 'flask', 'express', 'gin', 'echo'],
                'patterns': ['api', 'rest', 'endpoint', 'router'],
                'files': ['server.py', 'app.py', 'main.go', 'server.js']
            },
            ProjectType.CLI_TOOL: {
                'patterns': ['cli', 'command', 'argparse', 'typer'],
                'files': ['main.py', 'cli.py', 'cmd/', 'bin/'],
                'functions': ['main', 'cli', 'parse_args']
            },
            ProjectType.LIBRARY: {
                'patterns': ['lib', 'package', 'module', 'sdk'],
                'files': ['__init__.py', 'setup.py', 'lib.rs', 'index.js'],
                'structure': ['src/', 'lib/', 'pkg/']
            },
            ProjectType.DATA_PIPELINE: {
                'frameworks': ['airflow', 'luigi', 'dagster', 'prefect'],
                'patterns': ['pipeline', 'etl', 'data', 'transform'],
                'files': ['pipeline.py', 'dag.py', 'transform.py']
            },
            ProjectType.ML_MODEL: {
                'frameworks': ['tensorflow', 'pytorch', 'scikit-learn', 'keras'],
                'patterns': ['model', 'training', 'inference', 'prediction'],
                'files': ['model.py', 'train.py', 'predict.py']
            }
        }

        self.architecture_patterns = {
            Architecture.MICROSERVICES: {
                'indicators': ['docker', 'kubernetes', 'service', 'grpc'],
                'structure': ['services/', 'microservices/'],
                'config': ['docker-compose.yml', 'k8s/']
            },
            Architecture.LAYERED: {
                'structure': ['controllers/', 'services/', 'models/', 'repositories/'],
                'patterns': ['controller', 'service', 'repository', 'layer']
            },
            Architecture.MVC: {
                'structure': ['models/', 'views/', 'controllers/'],
                'patterns': ['model', 'view', 'controller', 'mvc']
            },
            Architecture.EVENT_DRIVEN: {
                'patterns': ['event', 'message', 'queue', 'pub', 'sub'],
                'frameworks': ['celery', 'rabbitmq', 'kafka', 'redis']
            }
        }

        self.nfr_patterns = {
            'performance': {
                'keywords': ['cache', 'async', 'concurrent', 'parallel', 'optimization'],
                'frameworks': ['redis', 'memcached', 'asyncio'],
                'patterns': ['@cache', 'async def', 'threading']
            },
            'security': {
                'keywords': ['auth', 'jwt', 'oauth', 'encryption', 'hash', 'security'],
                'frameworks': ['passport', 'bcrypt', 'cryptography'],
                'patterns': ['@login_required', '@authenticated', 'hash_password']
            },
            'scalability': {
                'keywords': ['load', 'scale', 'cluster', 'distributed'],
                'frameworks': ['kubernetes', 'docker', 'nginx'],
                'config': ['load_balancer', 'replica']
            },
            'reliability': {
                'keywords': ['retry', 'fallback', 'circuit_breaker', 'timeout'],
                'patterns': ['try_except', 'error_handling', 'logging']
            }
        }

    def infer_from_analysis(
        self,
        scan_result: ScanResult,
        api_schema: APISchema,
        model_schema: ModelSchema,
        test_schema: TestSchema,
        config_schema: ConfigSchema
    ) -> InferenceResult:
        """Infer high-level patterns and requirements from analysis results."""

        # Determine project type and architecture
        project_type = self._infer_project_type(scan_result, api_schema, config_schema)
        architecture = self._infer_architecture(scan_result, api_schema, config_schema)

        # Generate primary purpose
        primary_purpose = self._infer_primary_purpose(project_type, api_schema, model_schema)

        # Identify target users
        target_users = self._infer_target_users(project_type, api_schema)

        # Extract functional requirements
        functional_requirements = self._extract_functional_requirements(
            api_schema, model_schema, test_schema
        )

        # Extract non-functional requirements
        non_functional_requirements = self._extract_non_functional_requirements(
            scan_result, config_schema, test_schema
        )

        # Generate user stories
        user_stories = self._generate_user_stories(api_schema, functional_requirements)

        # Identify technical decisions
        technical_decisions = self._identify_technical_decisions(
            scan_result, config_schema
        )

        # Extract key entities
        key_entities = [model.name for model in model_schema.models]

        # Identify integrations
        integrations = list(config_schema.external_services.keys())

        # Identify deployment targets
        deployment_targets = [target.name for target in config_schema.deployment_targets]

        # Extract performance requirements
        performance_requirements = self._extract_performance_requirements(
            config_schema, test_schema
        )

        # Extract security considerations
        security_considerations = self._extract_security_considerations(
            api_schema, config_schema
        )

        return InferenceResult(
            project_type=project_type,
            architecture=architecture,
            primary_purpose=primary_purpose,
            target_users=target_users,
            functional_requirements=functional_requirements,
            non_functional_requirements=non_functional_requirements,
            user_stories=user_stories,
            technical_decisions=technical_decisions,
            key_entities=key_entities,
            integrations=integrations,
            deployment_targets=deployment_targets,
            performance_requirements=performance_requirements,
            security_considerations=security_considerations
        )

    def _infer_project_type(
        self,
        scan_result: ScanResult,
        api_schema: APISchema,
        config_schema: ConfigSchema
    ) -> ProjectType:
        """Infer the project type from scan results."""

        # Score each project type
        scores = {}

        for project_type, indicators in self.project_type_indicators.items():
            score = 0

            # Check frameworks
            frameworks = indicators.get('frameworks', [])
            for framework in frameworks:
                if framework in scan_result.frameworks:
                    score += 3

            # Check patterns in files
            patterns = indicators.get('patterns', [])
            for pattern in patterns:
                if any(pattern in str(file.path).lower() for file in scan_result.files):
                    score += 1

            # Check specific files
            files = indicators.get('files', [])
            for file_pattern in files:
                if any(file_pattern in file.path.name.lower() for file in scan_result.files):
                    score += 2

            # Check API endpoints
            if project_type == ProjectType.REST_API and api_schema.endpoints:
                score += len(api_schema.endpoints)

            # Check CLI patterns
            if project_type == ProjectType.CLI_TOOL:
                cli_indicators = ['main', 'cli', 'command', 'argparse', 'typer']
                for file in scan_result.files:
                    if any(indicator in str(file.path).lower() for indicator in cli_indicators):
                        score += 2

            scores[project_type] = score

        # Return the highest scoring type
        if scores:
            return max(scores, key=scores.get)

        return ProjectType.UNKNOWN

    def _infer_architecture(
        self,
        scan_result: ScanResult,
        api_schema: APISchema,
        config_schema: ConfigSchema
    ) -> Architecture:
        """Infer the architectural pattern."""

        scores = {}

        for arch_type, indicators in self.architecture_patterns.items():
            score = 0

            # Check indicators
            arch_indicators = indicators.get('indicators', [])
            for indicator in arch_indicators:
                if indicator in scan_result.frameworks:
                    score += 2
                if any(indicator in str(file.path).lower() for file in scan_result.files):
                    score += 1

            # Check structure
            structure = indicators.get('structure', [])
            for struct_pattern in structure:
                if any(struct_pattern in str(file.path) for file in scan_result.files):
                    score += 2

            # Check configuration
            config_patterns = indicators.get('config', [])
            for config_file in config_schema.config_files:
                for pattern in config_patterns:
                    if pattern in str(config_file).lower():
                        score += 3

            scores[arch_type] = score

        # Special case: if multiple services detected
        if len([f for f in scan_result.files if 'service' in str(f.path).lower()]) > 3:
            scores[Architecture.MICROSERVICES] = scores.get(Architecture.MICROSERVICES, 0) + 5

        if scores:
            return max(scores, key=scores.get)

        return Architecture.MONOLITHIC  # Default

    def _infer_primary_purpose(
        self,
        project_type: ProjectType,
        api_schema: APISchema,
        model_schema: ModelSchema
    ) -> str:
        """Infer the primary purpose of the project."""

        if project_type == ProjectType.REST_API:
            if api_schema.endpoints:
                # Analyze endpoint patterns to infer purpose
                paths = [endpoint.path for endpoint in api_schema.endpoints]
                if any('user' in path.lower() for path in paths):
                    return "User management and authentication API"
                elif any('order' in path.lower() for path in paths):
                    return "E-commerce order processing API"
                elif any('post' in path.lower() or 'article' in path.lower() for path in paths):
                    return "Content management API"
                else:
                    return f"RESTful API for {api_schema.name}"

        elif project_type == ProjectType.WEB_APPLICATION:
            return "Web application providing user interface and functionality"

        elif project_type == ProjectType.CLI_TOOL:
            return "Command-line tool for automation and user interaction"

        elif project_type == ProjectType.LIBRARY:
            return "Reusable library providing specific functionality to other applications"

        elif project_type == ProjectType.DATA_PIPELINE:
            return "Data processing pipeline for ETL operations"

        elif project_type == ProjectType.ML_MODEL:
            return "Machine learning model for prediction and analysis"

        else:
            # Try to infer from models
            if model_schema.models:
                model_names = [model.name.lower() for model in model_schema.models]
                if any('user' in name for name in model_names):
                    return "User-centric application with data management"
                elif any('product' in name for name in model_names):
                    return "Product catalog and management system"
                else:
                    return f"Data-driven application managing {', '.join(model_names[:3])}"

        return "Software application providing specific functionality"

    def _infer_target_users(self, project_type: ProjectType, api_schema: APISchema) -> List[str]:
        """Infer target users from project characteristics."""

        users = []

        if project_type == ProjectType.REST_API:
            users.extend(["API consumers", "Frontend developers", "Mobile app developers"])

        elif project_type == ProjectType.WEB_APPLICATION:
            users.extend(["End users", "Web application users"])

        elif project_type == ProjectType.CLI_TOOL:
            users.extend(["Developers", "System administrators", "DevOps engineers"])

        elif project_type == ProjectType.LIBRARY:
            users.extend(["Software developers", "Library consumers"])

        # Analyze API endpoints for more specific users
        if api_schema.endpoints:
            paths = [endpoint.path.lower() for endpoint in api_schema.endpoints]

            if any('admin' in path for path in paths):
                users.append("Administrators")
            if any('user' in path for path in paths):
                users.append("Registered users")
            if any('public' in path for path in paths):
                users.append("Public users")
            if any('auth' in path for path in paths):
                users.append("Authenticated users")

        return list(set(users)) if users else ["End users"]

    def _extract_functional_requirements(
        self,
        api_schema: APISchema,
        model_schema: ModelSchema,
        test_schema: TestSchema
    ) -> List[FunctionalRequirement]:
        """Extract functional requirements from API and model schemas."""

        requirements = []
        req_id = 1

        # Generate requirements from API endpoints
        endpoint_groups = self._group_endpoints_by_resource(api_schema.endpoints)

        for resource, endpoints in endpoint_groups.items():
            # Create CRUD requirements for each resource
            operations = [endpoint.method for endpoint in endpoints]

            if 'POST' in operations:
                requirements.append(FunctionalRequirement(
                    id=f"FR-{req_id:03d}",
                    title=f"Create {resource.title()}",
                    description=f"Users should be able to create new {resource} entries",
                    user_stories=[],
                    acceptance_criteria=[
                        f"System accepts valid {resource} data",
                        f"System validates {resource} input",
                        f"System returns created {resource} with ID"
                    ],
                    dependencies=[],
                    api_endpoints=[f"{ep.method} {ep.path}" for ep in endpoints if ep.method == 'POST'],
                    data_models=[resource.title()]
                ))
                req_id += 1

            if 'GET' in operations:
                requirements.append(FunctionalRequirement(
                    id=f"FR-{req_id:03d}",
                    title=f"Retrieve {resource.title()}",
                    description=f"Users should be able to retrieve {resource} information",
                    user_stories=[],
                    acceptance_criteria=[
                        f"System returns {resource} data when requested",
                        f"System handles non-existent {resource} appropriately",
                        f"System supports filtering and pagination"
                    ],
                    dependencies=[],
                    api_endpoints=[f"{ep.method} {ep.path}" for ep in endpoints if ep.method == 'GET'],
                    data_models=[resource.title()]
                ))
                req_id += 1

            if 'PUT' in operations or 'PATCH' in operations:
                requirements.append(FunctionalRequirement(
                    id=f"FR-{req_id:03d}",
                    title=f"Update {resource.title()}",
                    description=f"Users should be able to update existing {resource} entries",
                    user_stories=[],
                    acceptance_criteria=[
                        f"System accepts valid {resource} updates",
                        f"System validates update data",
                        f"System returns updated {resource}"
                    ],
                    dependencies=[],
                    api_endpoints=[f"{ep.method} {ep.path}" for ep in endpoints
                                 if ep.method in ['PUT', 'PATCH']],
                    data_models=[resource.title()]
                ))
                req_id += 1

            if 'DELETE' in operations:
                requirements.append(FunctionalRequirement(
                    id=f"FR-{req_id:03d}",
                    title=f"Delete {resource.title()}",
                    description=f"Users should be able to delete {resource} entries",
                    user_stories=[],
                    acceptance_criteria=[
                        f"System removes {resource} when requested",
                        f"System handles deletion of non-existent {resource}",
                        f"System confirms successful deletion"
                    ],
                    dependencies=[],
                    api_endpoints=[f"{ep.method} {ep.path}" for ep in endpoints if ep.method == 'DELETE'],
                    data_models=[resource.title()]
                ))
                req_id += 1

        # Generate requirements from models without corresponding endpoints
        covered_models = set()
        for req in requirements:
            covered_models.update(req.data_models)

        for model in model_schema.models:
            if model.name not in covered_models:
                requirements.append(FunctionalRequirement(
                    id=f"FR-{req_id:03d}",
                    title=f"Manage {model.name}",
                    description=f"System should manage {model.name} data and relationships",
                    user_stories=[],
                    acceptance_criteria=[
                        f"System stores {model.name} data",
                        f"System validates {model.name} fields",
                        f"System maintains {model.name} relationships"
                    ],
                    dependencies=[],
                    api_endpoints=[],
                    data_models=[model.name]
                ))
                req_id += 1

        return requirements

    def _group_endpoints_by_resource(self, endpoints) -> Dict[str, List]:
        """Group API endpoints by resource type."""

        groups = {}

        for endpoint in endpoints:
            # Extract resource name from path
            path_parts = endpoint.path.strip('/').split('/')
            resource = path_parts[0] if path_parts else 'root'

            # Remove API prefix
            if resource.lower() in ['api', 'v1', 'v2']:
                resource = path_parts[1] if len(path_parts) > 1 else 'root'

            # Remove ID patterns
            if '{' in resource or resource.isdigit():
                resource = 'item'

            if resource not in groups:
                groups[resource] = []

            groups[resource].append(endpoint)

        return groups

    def _extract_non_functional_requirements(
        self,
        scan_result: ScanResult,
        config_schema: ConfigSchema,
        test_schema: TestSchema
    ) -> List[NonFunctionalRequirement]:
        """Extract non-functional requirements from code patterns."""

        nfrs = []

        # Analyze each NFR category
        for category, patterns in self.nfr_patterns.items():
            evidence = []

            # Check keywords in files
            keywords = patterns.get('keywords', [])
            for file in scan_result.files:
                for keyword in keywords:
                    if keyword in str(file.path).lower():
                        evidence.append(f"Keyword '{keyword}' found in {file.path}")
                        break  # Avoid duplicate evidence for same file

            # Check frameworks
            frameworks = patterns.get('frameworks', [])
            for framework in frameworks:
                if framework in scan_result.frameworks:
                    evidence.append(f"Framework '{framework}' indicates {category} focus")

            # Check configuration
            if category == 'performance':
                if 'redis' in config_schema.external_services:
                    evidence.append("Redis caching configured for performance")

            if category == 'security':
                if any('auth' in section.name.lower() for section in config_schema.sections):
                    evidence.append("Authentication configuration present")

            if category == 'scalability':
                if any('docker' in target.type for target in config_schema.deployment_targets):
                    evidence.append("Docker deployment supports scalability")

            # Check test coverage for reliability
            if category == 'reliability' and test_schema.coverage.coverage_percentage:
                coverage = test_schema.coverage.coverage_percentage
                evidence.append(f"Test coverage at {coverage:.1f}% indicates reliability focus")

            if evidence:
                nfrs.append(NonFunctionalRequirement(
                    category=category.title(),
                    requirement=self._generate_nfr_statement(category),
                    description=self._generate_nfr_description(category, evidence),
                    metrics=self._suggest_nfr_metrics(category),
                    constraints=self._suggest_nfr_constraints(category),
                    evidence=evidence
                ))

        return nfrs

    def _generate_nfr_statement(self, category: str) -> str:
        """Generate NFR statement for category."""

        statements = {
            'performance': "System should respond to requests within acceptable time limits",
            'security': "System should protect user data and prevent unauthorized access",
            'scalability': "System should handle increasing load without degradation",
            'reliability': "System should be available and handle errors gracefully"
        }

        return statements.get(category, f"System should meet {category} requirements")

    def _generate_nfr_description(self, category: str, evidence: List[str]) -> str:
        """Generate detailed NFR description."""

        base_descriptions = {
            'performance': "The system implements caching, async processing, and optimization patterns",
            'security': "The system includes authentication, authorization, and data protection mechanisms",
            'scalability': "The system is designed with horizontal scaling and load distribution in mind",
            'reliability': "The system includes error handling, logging, and testing for stability"
        }

        base = base_descriptions.get(category, f"The system addresses {category} concerns")
        evidence_text = ". Evidence includes: " + "; ".join(evidence[:3]) if evidence else ""

        return base + evidence_text

    def _suggest_nfr_metrics(self, category: str) -> List[str]:
        """Suggest metrics for NFR category."""

        metrics = {
            'performance': [
                "Response time < 200ms for 95% of requests",
                "Throughput > 1000 requests/second",
                "Memory usage < 512MB"
            ],
            'security': [
                "Zero security vulnerabilities in production",
                "All API endpoints require authentication",
                "Data encrypted in transit and at rest"
            ],
            'scalability': [
                "Support 10x current user load",
                "Auto-scaling based on CPU/memory usage",
                "Database queries optimized for scale"
            ],
            'reliability': [
                "99.9% uptime availability",
                "Mean time to recovery < 15 minutes",
                "Error rate < 0.1%"
            ]
        }

        return metrics.get(category, [])

    def _suggest_nfr_constraints(self, category: str) -> List[str]:
        """Suggest constraints for NFR category."""

        constraints = {
            'performance': [
                "No single request should take longer than 30 seconds",
                "Database queries must be optimized",
                "Static assets must be cached"
            ],
            'security': [
                "All user input must be validated",
                "Passwords must be securely hashed",
                "API keys must be rotated regularly"
            ],
            'scalability': [
                "Stateless application design required",
                "Database connections must be pooled",
                "Horizontal scaling must be supported"
            ],
            'reliability': [
                "All errors must be logged",
                "Graceful degradation required",
                "Health checks must be implemented"
            ]
        }

        return constraints.get(category, [])

    def _generate_user_stories(
        self,
        api_schema: APISchema,
        functional_requirements: List[FunctionalRequirement]
    ) -> List[UserStory]:
        """Generate user stories from functional requirements."""

        user_stories = []

        for fr in functional_requirements:
            # Extract action from title
            action = fr.title.lower()

            if 'create' in action:
                user_stories.append(UserStory(
                    title=f"Create new {fr.data_models[0] if fr.data_models else 'item'}",
                    description=f"As a user, I want to create a new {fr.data_models[0].lower() if fr.data_models else 'item'} so that I can store the information in the system",
                    acceptance_criteria=fr.acceptance_criteria,
                    priority="High",
                    estimated_effort="Medium",
                    related_endpoints=[ep for ep in fr.api_endpoints if 'POST' in ep],
                    related_models=fr.data_models
                ))

            elif 'retrieve' in action or 'get' in action:
                user_stories.append(UserStory(
                    title=f"View {fr.data_models[0] if fr.data_models else 'item'} details",
                    description=f"As a user, I want to view {fr.data_models[0].lower() if fr.data_models else 'item'} details so that I can see the current information",
                    acceptance_criteria=fr.acceptance_criteria,
                    priority="High",
                    estimated_effort="Low",
                    related_endpoints=[ep for ep in fr.api_endpoints if 'GET' in ep],
                    related_models=fr.data_models
                ))

            elif 'update' in action:
                user_stories.append(UserStory(
                    title=f"Update {fr.data_models[0] if fr.data_models else 'item'}",
                    description=f"As a user, I want to update {fr.data_models[0].lower() if fr.data_models else 'item'} information so that I can keep the data current",
                    acceptance_criteria=fr.acceptance_criteria,
                    priority="Medium",
                    estimated_effort="Medium",
                    related_endpoints=[ep for ep in fr.api_endpoints if any(method in ep for method in ['PUT', 'PATCH'])],
                    related_models=fr.data_models
                ))

            elif 'delete' in action:
                user_stories.append(UserStory(
                    title=f"Delete {fr.data_models[0] if fr.data_models else 'item'}",
                    description=f"As a user, I want to delete {fr.data_models[0].lower() if fr.data_models else 'item'} so that I can remove unwanted data",
                    acceptance_criteria=fr.acceptance_criteria,
                    priority="Low",
                    estimated_effort="Low",
                    related_endpoints=[ep for ep in fr.api_endpoints if 'DELETE' in ep],
                    related_models=fr.data_models
                ))

        return user_stories

    def _identify_technical_decisions(
        self,
        scan_result: ScanResult,
        config_schema: ConfigSchema
    ) -> List[TechnicalDecision]:
        """Identify technical decisions made in the project."""

        decisions = []

        # Language choice
        if scan_result.languages:
            primary_language = list(scan_result.languages)[0]
            decisions.append(TechnicalDecision(
                title=f"Primary Programming Language: {primary_language}",
                description=f"The project uses {primary_language} as the main programming language",
                alternatives_considered=[],
                rationale="Based on project requirements and team expertise",
                consequences=[
                    f"Development follows {primary_language} conventions",
                    f"Dependencies are managed through {primary_language} ecosystem"
                ],
                evidence=[f"Majority of code files are {primary_language}"]
            ))

        # Framework choices
        if scan_result.frameworks:
            for framework in scan_result.frameworks:
                decisions.append(TechnicalDecision(
                    title=f"Framework Choice: {framework.title()}",
                    description=f"The project uses {framework} framework",
                    alternatives_considered=[],
                    rationale="Chosen for specific project requirements",
                    consequences=[
                        f"Architecture follows {framework} patterns",
                        f"Development team needs {framework} expertise"
                    ],
                    evidence=[f"Framework {framework} detected in dependencies"]
                ))

        # Database choice
        if config_schema.external_services:
            for service in config_schema.external_services:
                if service in ['postgresql', 'mysql', 'mongodb', 'sqlite']:
                    decisions.append(TechnicalDecision(
                        title=f"Database Technology: {service.title()}",
                        description=f"The project uses {service} for data persistence",
                        alternatives_considered=[],
                        rationale="Selected based on data structure and scalability needs",
                        consequences=[
                            f"Data modeling follows {service} patterns",
                            f"Queries written in {service} syntax"
                        ],
                        evidence=[f"Database {service} configuration found"]
                    ))

        # Deployment strategy
        deployment_types = [target.type for target in config_schema.deployment_targets]
        if deployment_types:
            decisions.append(TechnicalDecision(
                title="Deployment Strategy",
                description=f"The project uses {', '.join(deployment_types)} for deployment",
                alternatives_considered=[],
                rationale="Chosen for scalability and operational requirements",
                consequences=[
                    "Infrastructure as code approach",
                    "Automated deployment pipeline"
                ],
                evidence=[f"Deployment configurations: {', '.join(deployment_types)}"]
            ))

        return decisions

    def _extract_performance_requirements(
        self,
        config_schema: ConfigSchema,
        test_schema: TestSchema
    ) -> List[str]:
        """Extract performance requirements from configuration and tests."""

        requirements = []

        # Check for caching
        if 'redis' in config_schema.external_services:
            requirements.append("Implement caching for improved response times")

        # Check for async patterns
        if any('async' in str(file) for file in config_schema.config_files):
            requirements.append("Support asynchronous processing for better performance")

        # Check for performance tests
        perf_tests = [tc for tc in test_schema.test_cases
                     if any(keyword in tc.name.lower() for keyword in ['performance', 'load', 'benchmark'])]
        if perf_tests:
            requirements.append("Meet performance benchmarks defined in test suite")

        # Check for database optimization
        if config_schema.external_services:
            db_services = [s for s in config_schema.external_services
                          if s in ['postgresql', 'mysql', 'mongodb']]
            if db_services:
                requirements.append("Optimize database queries for performance")

        return requirements

    def _extract_security_considerations(
        self,
        api_schema: APISchema,
        config_schema: ConfigSchema
    ) -> List[str]:
        """Extract security considerations from API and configuration."""

        considerations = []

        # Authentication
        if 'JWT' in api_schema.authentication_schemes or 'OAuth' in api_schema.authentication_schemes:
            considerations.append("Implement secure authentication using JWT/OAuth")

        # HTTPS
        if any('https' in str(section) for section in config_schema.sections):
            considerations.append("Enforce HTTPS for all communications")

        # Input validation
        if api_schema.endpoints:
            considerations.append("Validate and sanitize all API inputs")

        # Secrets management
        if config_schema.secrets:
            considerations.append("Secure management of API keys and secrets")

        # Access control
        auth_endpoints = [ep for ep in api_schema.endpoints
                         if any(keyword in ep.path.lower() for keyword in ['auth', 'login', 'admin'])]
        if auth_endpoints:
            considerations.append("Implement proper access control and authorization")

        return considerations