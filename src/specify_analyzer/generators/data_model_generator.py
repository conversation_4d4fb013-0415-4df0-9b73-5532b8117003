"""Generator for data-model.md files from code analysis results."""

from pathlib import Path
from typing import Dict, List, Any, Optional

from ..inference import InferenceResult
from ..extractors.model_extractor import DataModel, ModelField, FieldType


class DataModelGenerator:
    """Generates data model documents from inference results."""

    def __init__(self):
        self.type_mappings = {
            FieldType.STRING: "String",
            FieldType.INTEGER: "Integer",
            FieldType.FLOAT: "Float",
            FieldType.BOOLEAN: "Boolean",
            FieldType.DATE: "Date",
            FieldType.DATETIME: "DateTime",
            FieldType.ARRAY: "Array",
            FieldType.OBJECT: "Object",
            FieldType.REFERENCE: "Reference",
            FieldType.UNKNOWN: "Unknown"
        }

    def generate_data_model_md(self, inference_result: InferenceResult, model_schema, project_name: str) -> str:
        """Generate complete data-model.md content from inference results."""

        sections = []

        # Header
        sections.append(self._generate_header(project_name, inference_result))

        # Core Entities
        sections.append(self._generate_core_entities(model_schema, inference_result))

        # Entity Relationships
        sections.append(self._generate_entity_relationships(model_schema, inference_result))

        # Data Flow
        sections.append(self._generate_data_flow(model_schema, inference_result))

        # Validation Rules
        sections.append(self._generate_validation_rules(model_schema, inference_result))

        # State Management
        sections.append(self._generate_state_management(model_schema, inference_result))

        # Data Persistence
        sections.append(self._generate_data_persistence(model_schema, inference_result))

        return "\n\n".join(sections)

    def _generate_header(self, project_name: str, inference_result: InferenceResult) -> str:
        """Generate document header."""

        return f"""---
description: \"Data model and entity definitions for {project_name}\"
scripts:
  sh: scripts/bash/validate-data-model.sh
  ps: scripts/powershell/validate-data-model.ps1
---

# Data Model: {project_name}

## Overview

This document defines the core data entities, relationships, and validation rules for the {inference_result.project_type.value.replace('_', ' ')} following {inference_result.architecture.value} architecture.

**Scope**: {len(inference_result.key_entities)} core entities with their fields, relationships, and business rules."""

    def _generate_core_entities(self, model_schema, inference_result: InferenceResult) -> str:
        """Generate core entities section."""

        content = ["## Core Entities"]

        if hasattr(model_schema, 'models') and model_schema.models:
            # Generate from actual detected models
            for i, model in enumerate(model_schema.models, 1):
                content.append(f"\n### {i}. {model.name}")

                if model.description:
                    content.append(f"**Description**: {model.description}")
                else:
                    content.append(f"**Description**: Core entity representing {model.name.lower()} data")

                # Fields table
                if model.fields:
                    content.append("\n**Fields**:")
                    content.append("| Field | Type | Required | Description | Constraints |")
                    content.append("|-------|------|----------|-------------|-------------|")

                    for field in model.fields:
                        field_type = self._get_field_type_display(field)
                        required = "Yes" if field.required else "No"
                        description = field.description or f"{field.name.title()} field"
                        constraints = self._format_constraints(field.constraints)

                        content.append(f"| {field.name} | {field_type} | {required} | {description} | {constraints} |")

                # Framework info
                if model.framework:
                    content.append(f"\n**Framework**: {model.framework.title()}")

                # Base classes
                if model.base_classes:
                    content.append(f"**Inheritance**: Extends {', '.join(model.base_classes)}")

                # Table name (if applicable)
                if model.table_name:
                    content.append(f"**Table Name**: `{model.table_name}`")

        else:
            # Generate from inferred entities
            for i, entity in enumerate(inference_result.key_entities, 1):
                content.append(f"\n### {i}. {entity}")
                content.append(f"**Description**: Core entity representing {entity.lower()} data")

                # Infer common fields based on entity name and project type
                fields = self._infer_entity_fields(entity, inference_result)

                content.append("\n**Fields**:")
                content.append("| Field | Type | Required | Description | Constraints |")
                content.append("|-------|------|----------|-------------|-------------|")

                for field in fields:
                    content.append(f"| {field['name']} | {field['type']} | {field['required']} | {field['description']} | {field['constraints']} |")

        return "\n".join(content)

    def _generate_entity_relationships(self, model_schema, inference_result: InferenceResult) -> str:
        """Generate entity relationships section."""

        content = ["## Entity Relationships"]

        if hasattr(model_schema, 'relationships') and model_schema.relationships:
            # Generate from actual detected relationships
            content.append("\n### Detected Relationships")

            for entity, related_entities in model_schema.relationships.items():
                content.append(f"\n**{entity}**:")
                for related in related_entities:
                    content.append(f"- Has relationship with {related}")

        else:
            # Infer relationships from entity names
            content.append("\n### Inferred Relationships")

            entities = inference_result.key_entities
            relationships = self._infer_relationships(entities)

            for relationship in relationships:
                content.append(f"- {relationship}")

        # Relationship types
        content.append("\n### Relationship Types")
        content.append("- **One-to-Many**: Parent entity can have multiple child entities")
        content.append("- **Many-to-Many**: Entities can be associated with multiple instances of each other")
        content.append("- **One-to-One**: Direct 1:1 relationship between entities")

        # Foreign keys
        content.append("\n### Foreign Key Constraints")
        content.append("- All relationships implemented through foreign key references")
        content.append("- Cascade delete behavior defined per relationship")
        content.append("- Referential integrity enforced at database level")

        return "\n".join(content)

    def _generate_data_flow(self, model_schema, inference_result: InferenceResult) -> str:
        """Generate data flow section."""

        content = ["## Data Flow"]

        # Input flow
        content.append("\n### Input Data Flow")
        if inference_result.project_type.value in ["rest_api", "web_application"]:
            content.append("1. **API Request** → Input validation → Business logic → Entity creation/update")
            content.append("2. **Form Submission** → Client validation → Server validation → Database persistence")
        elif inference_result.project_type.value == "cli_tool":
            content.append("1. **Command Input** → Argument parsing → Validation → Entity processing")
            content.append("2. **File Input** → File parsing → Data validation → Entity creation")
        else:
            content.append("1. **External Input** → Validation → Processing → Entity management")

        # Processing flow
        content.append("\n### Processing Data Flow")
        content.append("1. **Validation Layer**: Input sanitization and business rule validation")
        content.append("2. **Business Logic Layer**: Entity manipulation and relationship management")
        content.append("3. **Persistence Layer**: Database operations and transaction management")

        # Output flow
        content.append("\n### Output Data Flow")
        if inference_result.project_type.value in ["rest_api", "web_application"]:
            content.append("1. **Database Query** → Entity retrieval → Serialization → API Response")
            content.append("2. **Entity Collection** → Filtering/Pagination → Format conversion → Client delivery")
        elif inference_result.project_type.value == "cli_tool":
            content.append("1. **Entity Retrieval** → Formatting → Console output")
            content.append("2. **Report Generation** → Data aggregation → File output")
        else:
            content.append("1. **Entity Processing** → Format conversion → External delivery")

        # Error handling
        content.append("\n### Error Handling Flow")
        content.append("1. **Validation Errors**: Field-level validation with specific error messages")
        content.append("2. **Business Rule Violations**: Entity-level validation with contextual errors")
        content.append("3. **Persistence Errors**: Database constraint violations with recovery options")

        return "\n".join(content)

    def _generate_validation_rules(self, model_schema, inference_result: InferenceResult) -> str:
        """Generate validation rules section."""

        content = ["## Validation Rules"]

        # Field-level validation
        content.append("\n### Field-Level Validation")

        if hasattr(model_schema, 'models') and model_schema.models:
            for model in model_schema.models:
                if model.fields:
                    content.append(f"\n**{model.name}**:")
                    for field in model.fields:
                        validations = self._generate_field_validations(field)
                        if validations:
                            content.append(f"- **{field.name}**: {validations}")
        else:
            # Generic validation rules
            content.append("- **ID Fields**: Unique, non-null, positive integers")
            content.append("- **Name Fields**: Non-empty strings, max 255 characters")
            content.append("- **Email Fields**: Valid email format, unique")
            content.append("- **Date Fields**: Valid date format, future dates where applicable")

        # Entity-level validation
        content.append("\n### Entity-Level Validation")
        content.append("- **Uniqueness Constraints**: Business-specific unique combinations")
        content.append("- **Cross-Field Validation**: Dependent field validation rules")
        content.append("- **State Consistency**: Entity state transition validation")

        # Business rule validation
        content.append("\n### Business Rule Validation")

        # Infer business rules from functional requirements
        business_rules = self._infer_business_rules(inference_result)
        for rule in business_rules:
            content.append(f"- {rule}")

        return "\n".join(content)

    def _generate_state_management(self, model_schema, inference_result: InferenceResult) -> str:
        """Generate state management section."""

        content = ["## State Management"]

        # Entity states
        content.append("\n### Entity States")

        # Check if any entities suggest state management
        stateful_entities = []
        for entity in inference_result.key_entities:
            if any(keyword in entity.lower() for keyword in ['order', 'task', 'request', 'workflow']):
                stateful_entities.append(entity)

        if stateful_entities:
            for entity in stateful_entities:
                states = self._infer_entity_states(entity)
                content.append(f"\n**{entity}**:")
                for state in states:
                    content.append(f"- {state}")
        else:
            content.append("- **Created**: Initial entity creation state")
            content.append("- **Active**: Normal operational state")
            content.append("- **Inactive**: Temporarily disabled state")
            content.append("- **Deleted**: Soft deletion state")

        # State transitions
        content.append("\n### State Transitions")
        content.append("- **Valid Transitions**: Defined state machine with allowed transitions")
        content.append("- **Transition Guards**: Business rules governing state changes")
        content.append("- **Transition Actions**: Side effects triggered by state changes")

        # Audit trail
        content.append("\n### Audit Trail")
        content.append("- **Change Tracking**: Record all entity modifications")
        content.append("- **User Attribution**: Track who made changes")
        content.append("- **Timestamp Recording**: When changes occurred")
        content.append("- **Change History**: Full audit log for compliance")

        return "\n".join(content)

    def _generate_data_persistence(self, model_schema, inference_result: InferenceResult) -> str:
        """Generate data persistence section."""

        content = ["## Data Persistence"]

        # Database strategy
        content.append("\n### Database Strategy")

        # Determine database type from technical decisions or infer
        db_type = "Relational Database"
        for decision in inference_result.technical_decisions:
            if "database" in decision.title.lower():
                if "mongodb" in decision.title.lower():
                    db_type = "Document Database (MongoDB)"
                elif "postgresql" in decision.title.lower():
                    db_type = "Relational Database (PostgreSQL)"
                elif "mysql" in decision.title.lower():
                    db_type = "Relational Database (MySQL)"

        content.append(f"- **Database Type**: {db_type}")
        content.append("- **Transaction Management**: ACID compliance for data integrity")
        content.append("- **Connection Pooling**: Efficient database connection management")

        # Schema management
        content.append("\n### Schema Management")
        if hasattr(model_schema, 'migration_files') and model_schema.migration_files:
            content.append(f"- **Migration Files**: {len(model_schema.migration_files)} migration files detected")
        content.append("- **Version Control**: Database schema version tracking")
        content.append("- **Migration Strategy**: Forward and backward migration support")
        content.append("- **Environment Consistency**: Same schema across all environments")

        # Performance considerations
        content.append("\n### Performance Considerations")
        content.append("- **Indexing Strategy**: Optimized indexes for query performance")
        content.append("- **Query Optimization**: Efficient query patterns and joins")
        content.append("- **Caching Layer**: Strategic caching for frequently accessed data")

        # Backup and recovery
        content.append("\n### Backup and Recovery")
        content.append("- **Regular Backups**: Automated backup scheduling")
        content.append("- **Point-in-Time Recovery**: Ability to restore to specific timestamps")
        content.append("- **Disaster Recovery**: Cross-region backup strategy")
        content.append("- **Data Export**: Ability to export data in standard formats")

        return "\n".join(content)

    def _get_field_type_display(self, field: ModelField) -> str:
        """Get display name for field type."""
        if field.type_annotation:
            return field.type_annotation
        return self.type_mappings.get(field.field_type, "Unknown")

    def _format_constraints(self, constraints: Dict[str, Any]) -> str:
        """Format field constraints for display."""
        if not constraints:
            return "None"

        constraint_parts = []
        for key, value in constraints.items():
            if key == 'max_length':
                constraint_parts.append(f"Max {value} chars")
            elif key == 'min_length':
                constraint_parts.append(f"Min {value} chars")
            elif key == 'max_value':
                constraint_parts.append(f"Max {value}")
            elif key == 'min_value':
                constraint_parts.append(f"Min {value}")

        return ", ".join(constraint_parts) if constraint_parts else "None"

    def _infer_entity_fields(self, entity: str, inference_result: InferenceResult) -> List[Dict[str, str]]:
        """Infer common fields for an entity."""

        fields = []

        # Common ID field
        fields.append({
            'name': 'id',
            'type': 'Integer',
            'required': 'Yes',
            'description': f'Unique identifier for {entity.lower()}',
            'constraints': 'Primary key, auto-increment'
        })

        # Entity-specific fields based on name
        entity_lower = entity.lower()

        if 'user' in entity_lower:
            fields.extend([
                {
                    'name': 'email',
                    'type': 'String',
                    'required': 'Yes',
                    'description': 'User email address',
                    'constraints': 'Unique, valid email format'
                },
                {
                    'name': 'name',
                    'type': 'String',
                    'required': 'Yes',
                    'description': 'User full name',
                    'constraints': 'Max 255 characters'
                }
            ])

        elif 'product' in entity_lower:
            fields.extend([
                {
                    'name': 'name',
                    'type': 'String',
                    'required': 'Yes',
                    'description': 'Product name',
                    'constraints': 'Max 255 characters'
                },
                {
                    'name': 'price',
                    'type': 'Float',
                    'required': 'Yes',
                    'description': 'Product price',
                    'constraints': 'Positive value'
                }
            ])

        elif 'order' in entity_lower:
            fields.extend([
                {
                    'name': 'status',
                    'type': 'String',
                    'required': 'Yes',
                    'description': 'Order status',
                    'constraints': 'Enum: pending, confirmed, shipped, delivered'
                },
                {
                    'name': 'total',
                    'type': 'Float',
                    'required': 'Yes',
                    'description': 'Order total amount',
                    'constraints': 'Positive value'
                }
            ])

        else:
            # Generic fields
            fields.extend([
                {
                    'name': 'name',
                    'type': 'String',
                    'required': 'Yes',
                    'description': f'{entity} name',
                    'constraints': 'Max 255 characters'
                },
                {
                    'name': 'description',
                    'type': 'String',
                    'required': 'No',
                    'description': f'{entity} description',
                    'constraints': 'Max 1000 characters'
                }
            ])

        # Common timestamp fields
        fields.extend([
            {
                'name': 'created_at',
                'type': 'DateTime',
                'required': 'Yes',
                'description': 'Creation timestamp',
                'constraints': 'Auto-generated'
            },
            {
                'name': 'updated_at',
                'type': 'DateTime',
                'required': 'Yes',
                'description': 'Last update timestamp',
                'constraints': 'Auto-updated'
            }
        ])

        return fields

    def _infer_relationships(self, entities: List[str]) -> List[str]:
        """Infer relationships between entities."""

        relationships = []

        # Common relationship patterns
        for i, entity1 in enumerate(entities):
            for entity2 in entities[i+1:]:
                rel = self._determine_relationship(entity1, entity2)
                if rel:
                    relationships.append(rel)

        return relationships

    def _determine_relationship(self, entity1: str, entity2: str) -> Optional[str]:
        """Determine relationship between two entities."""

        e1_lower = entity1.lower()
        e2_lower = entity2.lower()

        # Common patterns
        if 'user' in e1_lower and 'order' in e2_lower:
            return f"{entity1} can have multiple {entity2}s (One-to-Many)"
        elif 'order' in e1_lower and 'product' in e2_lower:
            return f"{entity1} can contain multiple {entity2}s (Many-to-Many)"
        elif 'user' in e1_lower and 'profile' in e2_lower:
            return f"{entity1} has one {entity2} (One-to-One)"

        # Generic relationship
        if len(entity1) < len(entity2):
            return f"{entity1} can be associated with multiple {entity2}s"

        return None

    def _generate_field_validations(self, field: ModelField) -> str:
        """Generate validation rules for a field."""

        validations = []

        if field.required:
            validations.append("Required")

        if field.field_type == FieldType.STRING:
            validations.append("Non-empty string")
        elif field.field_type == FieldType.INTEGER:
            validations.append("Valid integer")
        elif field.field_type == FieldType.FLOAT:
            validations.append("Valid number")
        elif field.field_type == FieldType.BOOLEAN:
            validations.append("Boolean value")

        # Add constraint validations
        for constraint, value in field.constraints.items():
            if constraint == 'max_length':
                validations.append(f"Max {value} characters")
            elif constraint == 'min_length':
                validations.append(f"Min {value} characters")

        return ", ".join(validations) if validations else "Basic type validation"

    def _infer_business_rules(self, inference_result: InferenceResult) -> List[str]:
        """Infer business rules from functional requirements."""

        rules = []

        # Extract rules from functional requirements
        for fr in inference_result.functional_requirements:
            if 'create' in fr.title.lower():
                rules.append(f"New {fr.data_models[0] if fr.data_models else 'entity'} must pass all validation checks")
            elif 'update' in fr.title.lower():
                rules.append(f"Updates to {fr.data_models[0] if fr.data_models else 'entity'} must maintain data integrity")
            elif 'delete' in fr.title.lower():
                rules.append(f"Deletion of {fr.data_models[0] if fr.data_models else 'entity'} must handle dependent relationships")

        # Generic business rules
        if not rules:
            rules = [
                "All required fields must have valid values",
                "Business logic constraints must be enforced",
                "Referential integrity must be maintained"
            ]

        return rules

    def _infer_entity_states(self, entity: str) -> List[str]:
        """Infer possible states for an entity."""

        entity_lower = entity.lower()

        if 'order' in entity_lower:
            return ["Pending", "Confirmed", "Processing", "Shipped", "Delivered", "Cancelled"]
        elif 'task' in entity_lower:
            return ["Created", "In Progress", "Completed", "Cancelled"]
        elif 'request' in entity_lower:
            return ["Submitted", "Under Review", "Approved", "Rejected"]
        elif 'user' in entity_lower:
            return ["Active", "Inactive", "Suspended", "Deleted"]
        else:
            return ["Created", "Active", "Inactive", "Archived"]