"""Generator for research.md files from code analysis results."""

from pathlib import Path
from typing import Dict, List, Any, Optional

from ..inference import InferenceR<PERSON>ult, TechnicalDecision


class ResearchGenerator:
    """Generates research documents from inference results."""

    def generate_research_md(self, inference_result: InferenceResult, project_name: str) -> str:
        """Generate complete research.md content from inference results."""

        sections = []

        # Header
        sections.append(self._generate_header(project_name, inference_result))

        # Technology Decisions
        sections.append(self._generate_technology_decisions(inference_result))

        # Architecture Analysis
        sections.append(self._generate_architecture_analysis(inference_result))

        # Integration Research
        sections.append(self._generate_integration_research(inference_result))

        # Performance Considerations
        sections.append(self._generate_performance_research(inference_result))

        # Security Research
        sections.append(self._generate_security_research(inference_result))

        # Alternative Approaches
        sections.append(self._generate_alternatives_analysis(inference_result))

        # Implementation Risks
        sections.append(self._generate_risks_analysis(inference_result))

        # Future Research Areas
        sections.append(self._generate_future_research(inference_result))

        return "\n\n".join(sections)

    def _generate_header(self, project_name: str, inference_result: InferenceResult) -> str:
        """Generate document header."""

        return f"""---
description: \"Technical research and decisions for {project_name}\"
scripts:
  sh: scripts/bash/validate-research.sh
  ps: scripts/powershell/validate-research.ps1
---

# Research: {project_name}

## Overview

This document captures the technical research, decisions, and rationale for implementing the {inference_result.project_type.value.replace('_', ' ')} using {inference_result.architecture.value} architecture.

**Research Scope**: Technology stack validation, architecture patterns, integration approaches, and implementation strategies."""

    def _generate_technology_decisions(self, inference_result: InferenceResult) -> str:
        """Generate technology decisions section."""

        content = ["## Technology Decisions"]

        if inference_result.technical_decisions:
            for i, decision in enumerate(inference_result.technical_decisions, 1):
                content.append(f"\n### Decision {i}: {decision.title}")

                content.append(f"**Description**: {decision.description}")
                content.append(f"**Rationale**: {decision.rationale}")

                if decision.alternatives_considered:
                    content.append("\n**Alternatives Considered**:")
                    for alt in decision.alternatives_considered:
                        content.append(f"- {alt}")

                if decision.consequences:
                    content.append("\n**Consequences**:")
                    for consequence in decision.consequences:
                        content.append(f"- {consequence}")

                if decision.evidence:
                    content.append("\n**Evidence**:")
                    for evidence in decision.evidence:
                        content.append(f"- {evidence}")

        else:
            # Generate default technology research
            content.append("\n### Primary Technology Stack")
            content.append("**Decision**: Use detected technology stack from codebase analysis")
            content.append("**Rationale**: Maintain consistency with existing codebase and team expertise")
            content.append("**Evidence**: Existing code patterns and dependency management")

        return "\n".join(content)

    def _generate_architecture_analysis(self, inference_result: InferenceResult) -> str:
        """Generate architecture analysis section."""

        content = ["## Architecture Analysis"]

        content.append(f"\n### Selected Pattern: {inference_result.architecture.value.title()}")

        # Architecture-specific analysis
        if inference_result.architecture.value == "layered":
            content.append("**Description**: Traditional layered architecture with clear separation of concerns")
            content.append("**Benefits**:")
            content.append("- Clear separation between presentation, business, and data layers")
            content.append("- Easy to understand and maintain")
            content.append("- Good testability through layer isolation")
            content.append("**Trade-offs**:")
            content.append("- Can lead to performance overhead from layer traversal")
            content.append("- Risk of anemic domain models")

        elif inference_result.architecture.value == "microservices":
            content.append("**Description**: Distributed architecture with independent, deployable services")
            content.append("**Benefits**:")
            content.append("- Independent scaling and deployment")
            content.append("- Technology diversity across services")
            content.append("- Fault isolation and resilience")
            content.append("**Trade-offs**:")
            content.append("- Increased operational complexity")
            content.append("- Network latency and failure modes")
            content.append("- Data consistency challenges")

        elif inference_result.architecture.value == "event_driven":
            content.append("**Description**: Architecture based on event production, detection, and reaction")
            content.append("**Benefits**:")
            content.append("- Loose coupling between components")
            content.append("- High scalability and responsiveness")
            content.append("- Natural audit trail through events")
            content.append("**Trade-offs**:")
            content.append("- Complex debugging and tracing")
            content.append("- Eventual consistency challenges")

        else:
            content.append("**Description**: Monolithic architecture with all components in single deployable unit")
            content.append("**Benefits**:")
            content.append("- Simple deployment and testing")
            content.append("- Strong consistency guarantees")
            content.append("- Easy to understand and debug")
            content.append("**Trade-offs**:")
            content.append("- All components scale together")
            content.append("- Technology stack uniformity required")

        # Fit assessment
        content.append(f"\n### Architecture Fit Assessment")
        content.append(f"**Project Type**: {inference_result.project_type.value.replace('_', ' ').title()}")
        content.append(f"**Scale Requirements**: {len(inference_result.key_entities)} entities, {len(inference_result.integrations)} integrations")

        if len(inference_result.key_entities) <= 3 and len(inference_result.integrations) <= 2:
            content.append("**Assessment**: Architecture appropriate for project scale and complexity")
        else:
            content.append("**Assessment**: Architecture may need additional complexity management patterns")

        return "\n".join(content)

    def _generate_integration_research(self, inference_result: InferenceResult) -> str:
        """Generate integration research section."""

        content = ["## Integration Research"]

        if inference_result.integrations:
            content.append(f"\n### External Services ({len(inference_result.integrations)} identified)")

            for integration in inference_result.integrations:
                content.append(f"\n**{integration.title()}**:")

                # Service-specific research
                if integration.lower() == 'aws':
                    content.append("- **Type**: Cloud infrastructure services")
                    content.append("- **Authentication**: IAM roles and access keys")
                    content.append("- **SDK**: Boto3 for Python, AWS SDK for other languages")
                    content.append("- **Best Practices**: Use IAM roles, enable CloudTrail, implement retry logic")

                elif integration.lower() == 'stripe':
                    content.append("- **Type**: Payment processing service")
                    content.append("- **Authentication**: API keys (publishable and secret)")
                    content.append("- **SDK**: Official Stripe libraries available")
                    content.append("- **Best Practices**: Use webhooks, handle idempotency, test with sandbox")

                elif integration.lower() in ['postgresql', 'mysql']:
                    content.append("- **Type**: Relational database")
                    content.append("- **Connection**: Connection pooling recommended")
                    content.append("- **ORM**: SQLAlchemy for Python, appropriate ORM for other languages")
                    content.append("- **Best Practices**: Use migrations, implement connection retry, backup strategy")

                elif integration.lower() == 'redis':
                    content.append("- **Type**: In-memory data structure store")
                    content.append("- **Use Cases**: Caching, session storage, pub/sub")
                    content.append("- **Client**: Redis-py for Python, appropriate client for other languages")
                    content.append("- **Best Practices**: Set TTL, handle connection failures, monitor memory usage")

                else:
                    content.append("- **Type**: External service integration")
                    content.append("- **Authentication**: API key or OAuth-based authentication")
                    content.append("- **SDK**: Check for official SDK or REST API documentation")
                    content.append("- **Best Practices**: Implement retry logic, rate limiting, error handling")

            # Integration patterns
            content.append("\n### Integration Patterns")
            content.append("- **Circuit Breaker**: Prevent cascade failures from external service issues")
            content.append("- **Retry with Backoff**: Handle transient failures gracefully")
            content.append("- **Timeout Configuration**: Set appropriate timeouts for each service")
            content.append("- **Fallback Strategies**: Define behavior when external services are unavailable")

        else:
            content.append("\n### No External Integrations")
            content.append("**Finding**: System appears to be self-contained with no external service dependencies")
            content.append("**Benefits**: Reduced complexity, no external failure points")
            content.append("**Considerations**: May need external services for production deployment (monitoring, logging)")

        return "\n".join(content)

    def _generate_performance_research(self, inference_result: InferenceResult) -> str:
        """Generate performance research section."""

        content = ["## Performance Research"]

        if inference_result.performance_requirements:
            content.append("\n### Performance Requirements Analysis")
            for req in inference_result.performance_requirements:
                content.append(f"- {req}")

            content.append("\n### Performance Strategies")

            # Caching strategies
            if any('cache' in req.lower() for req in inference_result.performance_requirements):
                content.append("\n**Caching**:")
                content.append("- **Application-level**: In-memory caching for frequently accessed data")
                content.append("- **Database-level**: Query result caching and connection pooling")
                content.append("- **CDN**: Static asset caching for web applications")

            # Async processing
            if any('async' in req.lower() for req in inference_result.performance_requirements):
                content.append("\n**Asynchronous Processing**:")
                content.append("- **Background Tasks**: Queue-based processing for long-running operations")
                content.append("- **Non-blocking I/O**: Async/await patterns for I/O operations")
                content.append("- **Event-driven**: Reactive programming patterns")

            # Database optimization
            content.append("\n**Database Optimization**:")
            content.append("- **Indexing**: Strategic index creation for query performance")
            content.append("- **Query Optimization**: N+1 query prevention, efficient joins")
            content.append("- **Connection Management**: Connection pooling and reuse")

        else:
            content.append("\n### General Performance Considerations")
            content.append("**Database Performance**:")
            content.append("- Implement proper indexing strategy")
            content.append("- Use connection pooling")
            content.append("- Monitor query performance")

            content.append("\n**Application Performance**:")
            content.append("- Implement caching where appropriate")
            content.append("- Use async patterns for I/O operations")
            content.append("- Monitor resource usage")

        # Monitoring and measurement
        content.append("\n### Performance Monitoring")
        content.append("- **Metrics Collection**: Response times, throughput, error rates")
        content.append("- **Database Monitoring**: Query performance, connection usage")
        content.append("- **Resource Monitoring**: CPU, memory, disk usage")
        content.append("- **APM Tools**: Application Performance Monitoring integration")

        return "\n".join(content)

    def _generate_security_research(self, inference_result: InferenceResult) -> str:
        """Generate security research section."""

        content = ["## Security Research"]

        if inference_result.security_considerations:
            content.append("\n### Security Requirements")
            for consideration in inference_result.security_considerations:
                content.append(f"- {consideration}")

            content.append("\n### Security Implementation Strategy")

            # Authentication
            if any('auth' in consideration.lower() for consideration in inference_result.security_considerations):
                content.append("\n**Authentication**:")
                content.append("- **JWT Tokens**: Stateless authentication with proper token management")
                content.append("- **OAuth Integration**: Third-party authentication providers")
                content.append("- **Session Management**: Secure session handling and expiration")

            # Authorization
            content.append("\n**Authorization**:")
            content.append("- **Role-Based Access Control (RBAC)**: User roles and permissions")
            content.append("- **Resource-Level Permissions**: Fine-grained access control")
            content.append("- **API Security**: Endpoint-level authorization checks")

            # Data Protection
            content.append("\n**Data Protection**:")
            content.append("- **Encryption in Transit**: HTTPS/TLS for all communications")
            content.append("- **Encryption at Rest**: Database and file encryption")
            content.append("- **Sensitive Data Handling**: PII protection and masking")

        else:
            content.append("\n### Basic Security Measures")
            content.append("**Input Validation**:")
            content.append("- Validate and sanitize all user inputs")
            content.append("- Implement proper error handling")
            content.append("- Use parameterized queries to prevent SQL injection")

            content.append("\n**Authentication & Authorization**:")
            content.append("- Implement secure user authentication")
            content.append("- Use strong password policies")
            content.append("- Implement proper session management")

        # Security best practices
        content.append("\n### Security Best Practices")
        content.append("- **OWASP Guidelines**: Follow OWASP Top 10 security recommendations")
        content.append("- **Security Headers**: Implement proper HTTP security headers")
        content.append("- **Dependency Scanning**: Regular security updates for dependencies")
        content.append("- **Secrets Management**: Secure handling of API keys and credentials")

        return "\n".join(content)

    def _generate_alternatives_analysis(self, inference_result: InferenceResult) -> str:
        """Generate alternatives analysis section."""

        content = ["## Alternative Approaches Considered"]

        # Architecture alternatives
        content.append(f"\n### Architecture Alternatives to {inference_result.architecture.value.title()}")

        if inference_result.architecture.value == "monolithic":
            content.append("**Microservices Architecture**:")
            content.append("- *Pros*: Independent scaling, technology diversity")
            content.append("- *Cons*: Increased complexity, operational overhead")
            content.append("- *Decision*: Rejected due to project scale not justifying complexity")

        elif inference_result.architecture.value == "microservices":
            content.append("**Monolithic Architecture**:")
            content.append("- *Pros*: Simpler deployment, strong consistency")
            content.append("- *Cons*: Coupled scaling, technology uniformity")
            content.append("- *Decision*: Rejected due to scale requirements and team distribution")

        # Technology alternatives
        content.append(f"\n### Technology Stack Alternatives")

        # Language alternatives
        if inference_result.technical_decisions:
            primary_tech = inference_result.technical_decisions[0]
            if "python" in primary_tech.title.lower():
                content.append("**Alternative Languages**:")
                content.append("- *JavaScript/Node.js*: Unified language across stack")
                content.append("- *Go*: Better performance for concurrent operations")
                content.append("- *Java*: Enterprise ecosystem and tooling")
                content.append("- *Decision*: Python chosen for team expertise and rapid development")

        # Database alternatives
        if any('database' in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("\n**Database Alternatives**:")
            content.append("- *NoSQL (MongoDB)*: Flexible schema, horizontal scaling")
            content.append("- *In-memory (Redis)*: Ultra-high performance, data volatility")
            content.append("- *Graph (Neo4j)*: Complex relationship modeling")
            content.append("- *Decision*: Relational database chosen for ACID properties and familiar tooling")

        return "\n".join(content)

    def _generate_risks_analysis(self, inference_result: InferenceResult) -> str:
        """Generate risks analysis section."""

        content = ["## Implementation Risks"]

        # Technical risks
        content.append("\n### Technical Risks")

        # Complexity risks
        entity_count = len(inference_result.key_entities)
        if entity_count > 5:
            content.append(f"**High Entity Complexity ({entity_count} entities)**:")
            content.append("- *Risk*: Complex data relationships and transaction management")
            content.append("- *Mitigation*: Careful domain modeling, comprehensive testing")

        integration_count = len(inference_result.integrations)
        if integration_count > 3:
            content.append(f"**Multiple External Dependencies ({integration_count} services)**:")
            content.append("- *Risk*: Cascade failures, integration complexity")
            content.append("- *Mitigation*: Circuit breakers, fallback strategies, monitoring")

        # Performance risks
        if inference_result.performance_requirements:
            content.append("\n**Performance Risks**:")
            content.append("- *Risk*: Meeting performance requirements under load")
            content.append("- *Mitigation*: Load testing, performance monitoring, optimization")

        # Security risks
        if inference_result.security_considerations:
            content.append("\n**Security Risks**:")
            content.append("- *Risk*: Security vulnerabilities in authentication/authorization")
            content.append("- *Mitigation*: Security audits, penetration testing, OWASP compliance")

        # Operational risks
        content.append("\n### Operational Risks")
        content.append("**Deployment Complexity**:")
        content.append("- *Risk*: Complex deployment process leading to errors")
        content.append("- *Mitigation*: Infrastructure as code, automated deployment pipelines")

        content.append("\n**Monitoring and Observability**:")
        content.append("- *Risk*: Insufficient visibility into system behavior")
        content.append("- *Mitigation*: Comprehensive logging, metrics, distributed tracing")

        # Team risks
        content.append("\n### Team and Process Risks")
        content.append("**Technology Expertise**:")
        content.append("- *Risk*: Team unfamiliarity with chosen technologies")
        content.append("- *Mitigation*: Training, documentation, pair programming")

        content.append("\n**Scope Creep**:")
        content.append("- *Risk*: Requirements expansion during implementation")
        content.append("- *Mitigation*: Clear acceptance criteria, regular stakeholder communication")

        return "\n".join(content)

    def _generate_future_research(self, inference_result: InferenceResult) -> str:
        """Generate future research areas section."""

        content = ["## Future Research Areas"]

        # Scalability research
        content.append("\n### Scalability Enhancements")
        content.append("- **Horizontal Scaling**: Research auto-scaling strategies")
        content.append("- **Database Sharding**: Investigate data partitioning approaches")
        content.append("- **Caching Evolution**: Advanced caching strategies and cache invalidation")

        # Technology evolution
        content.append("\n### Technology Evolution")
        content.append("- **Emerging Frameworks**: Monitor new framework releases and capabilities")
        content.append("- **Cloud-Native Patterns**: Investigate serverless and container orchestration")
        content.append("- **AI/ML Integration**: Research opportunities for intelligent features")

        # Performance optimization
        content.append("\n### Performance Optimization")
        content.append("- **Query Optimization**: Advanced database optimization techniques")
        content.append("- **CDN Integration**: Content delivery network implementation")
        content.append("- **Edge Computing**: Evaluate edge deployment opportunities")

        # Security enhancements
        content.append("\n### Security Enhancements")
        content.append("- **Zero Trust Architecture**: Research zero trust security models")
        content.append("- **Advanced Monitoring**: Security event correlation and threat detection")
        content.append("- **Compliance Frameworks**: Industry-specific compliance requirements")

        # Operational improvements
        content.append("\n### Operational Improvements")
        content.append("- **Observability**: Advanced monitoring and alerting strategies")
        content.append("- **Chaos Engineering**: Fault injection and resilience testing")
        content.append("- **Cost Optimization**: Resource usage optimization and cost monitoring")

        # Integration opportunities
        if len(inference_result.integrations) < 5:
            content.append("\n### Integration Opportunities")
            content.append("- **Analytics Platforms**: Data analysis and business intelligence")
            content.append("- **Notification Services**: Email, SMS, and push notification providers")
            content.append("- **Search Solutions**: Advanced search and indexing capabilities")

        return "\n".join(content)