"""Generator for quickstart.md files from code analysis results."""

from pathlib import Path
from typing import Dict, List, Any, Optional

from ..inference import InferenceResult, UserStory


class QuickstartGenerator:
    """Generates quickstart documents from inference results."""

    def generate_quickstart_md(self, inference_result: InferenceResult, project_name: str) -> str:
        """Generate complete quickstart.md content from inference results."""

        sections = []

        # Header
        sections.append(self._generate_header(project_name, inference_result))

        # Prerequisites
        sections.append(self._generate_prerequisites(inference_result))

        # Quick Setup
        sections.append(self._generate_quick_setup(inference_result, project_name))

        # Core Workflows
        sections.append(self._generate_core_workflows(inference_result))

        # Testing Guide
        sections.append(self._generate_testing_guide(inference_result))

        # Troubleshooting
        sections.append(self._generate_troubleshooting(inference_result))

        # Performance Validation
        sections.append(self._generate_performance_validation(inference_result))

        # Next Steps
        sections.append(self._generate_next_steps(inference_result))

        return "\n\n".join(sections)

    def _generate_header(self, project_name: str, inference_result: InferenceResult) -> str:
        """Generate document header."""

        return f"""---
description: \"Quickstart guide for {project_name}\"
scripts:
  sh: scripts/bash/quickstart-demo.sh
  ps: scripts/powershell/quickstart-demo.ps1
---

# Quickstart: {project_name}

## Overview

This guide provides a fast path to get {project_name} up and running, demonstrating core functionality through practical examples.

**Project Type**: {inference_result.project_type.value.replace('_', ' ').title()}
**Architecture**: {inference_result.architecture.value.title()}
**Estimated Setup Time**: 10-15 minutes"""

    def _generate_prerequisites(self, inference_result: InferenceResult) -> str:
        """Generate prerequisites section."""

        content = ["## Prerequisites"]

        # System requirements
        content.append("\n### System Requirements")

        # Determine requirements based on technical decisions
        languages = []
        databases = []
        tools = []

        for decision in inference_result.technical_decisions:
            if "language" in decision.title.lower():
                if "python" in decision.title.lower():
                    languages.append("Python 3.8+")
                elif "javascript" in decision.title.lower() or "node" in decision.title.lower():
                    languages.append("Node.js 16+")
                elif "go" in decision.title.lower():
                    languages.append("Go 1.19+")
            elif "database" in decision.title.lower():
                if "postgresql" in decision.title.lower():
                    databases.append("PostgreSQL 12+")
                elif "mysql" in decision.title.lower():
                    databases.append("MySQL 8+")
                elif "mongodb" in decision.title.lower():
                    databases.append("MongoDB 5+")

        # Default requirements if none detected
        if not languages:
            languages = ["Python 3.8+ (inferred from project analysis)"]

        for lang in languages:
            content.append(f"- {lang}")

        content.append("- Git")

        if databases:
            for db in databases:
                content.append(f"- {db}")

        # Development tools
        content.append("\n### Development Tools")

        if inference_result.project_type.value in ["rest_api", "web_application"]:
            content.append("- API testing tool (Postman, curl, or httpie)")

        if inference_result.project_type.value == "cli_tool":
            content.append("- Terminal/Command prompt")

        # External services
        if inference_result.integrations:
            content.append("\n### External Service Access")
            for integration in inference_result.integrations[:3]:
                content.append(f"- {integration.title()} account and API credentials")

        return "\n".join(content)

    def _generate_quick_setup(self, inference_result: InferenceResult, project_name: str) -> str:
        """Generate quick setup section."""

        content = ["## Quick Setup"]

        # Repository setup
        content.append("\n### 1. Clone and Setup")
        content.append("```bash")
        content.append("git clone <repository-url>")
        content.append(f"cd {project_name.lower().replace(' ', '-')}")
        content.append("```")

        # Environment setup based on project type
        content.append("\n### 2. Environment Setup")

        # Python setup
        if any("python" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("```bash")
            content.append("# Create virtual environment")
            content.append("python -m venv venv")
            content.append("source venv/bin/activate  # On Windows: venv\\Scripts\\activate")
            content.append("")
            content.append("# Install dependencies")
            content.append("pip install -r requirements.txt")
            content.append("# OR")
            content.append("pip install -e .")
            content.append("```")

        # Node.js setup
        elif any("javascript" in decision.title.lower() or "node" in decision.title.lower()
                for decision in inference_result.technical_decisions):
            content.append("```bash")
            content.append("# Install dependencies")
            content.append("npm install")
            content.append("# OR")
            content.append("yarn install")
            content.append("```")

        # Go setup
        elif any("go" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("```bash")
            content.append("# Download dependencies")
            content.append("go mod download")
            content.append("```")

        else:
            # Generic setup
            content.append("```bash")
            content.append("# Follow project-specific setup instructions")
            content.append("# Check README.md for detailed setup steps")
            content.append("```")

        # Database setup
        if any("database" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("\n### 3. Database Setup")
            content.append("```bash")
            content.append("# Start database (if using Docker)")
            content.append("docker-compose up -d database")
            content.append("")
            content.append("# Run migrations")
            content.append("# Python/Django: python manage.py migrate")
            content.append("# Python/Alembic: alembic upgrade head")
            content.append("# Node.js: npm run migrate")
            content.append("# Go: go run migrations/migrate.go")
            content.append("```")

        # Environment variables
        content.append("\n### 4. Configuration")
        content.append("```bash")
        content.append("# Copy environment template")
        content.append("cp .env.example .env")
        content.append("")
        content.append("# Edit configuration")
        content.append("# Set database URL, API keys, etc.")
        content.append("```")

        # Start the application
        content.append("\n### 5. Start Application")

        if inference_result.project_type.value == "rest_api":
            content.append("```bash")
            content.append("# Start the server")
            content.append("# Python: python app.py or uvicorn main:app")
            content.append("# Node.js: npm start or node server.js")
            content.append("# Go: go run main.go")
            content.append("")
            content.append("# Server should be running on http://localhost:8000")
            content.append("```")

        elif inference_result.project_type.value == "web_application":
            content.append("```bash")
            content.append("# Start development server")
            content.append("# Frontend: npm run dev")
            content.append("# Backend: python app.py")
            content.append("")
            content.append("# Application should be running on http://localhost:3000")
            content.append("```")

        elif inference_result.project_type.value == "cli_tool":
            content.append("```bash")
            content.append("# Test CLI installation")
            content.append("python -m src.main --help")
            content.append("# OR")
            content.append("./cli --help")
            content.append("```")

        return "\n".join(content)

    def _generate_core_workflows(self, inference_result: InferenceResult) -> str:
        """Generate core workflows section."""

        content = ["## Core Workflows"]

        # Generate workflows based on user stories
        if inference_result.user_stories:
            for i, story in enumerate(inference_result.user_stories[:5], 1):
                content.append(f"\n### Workflow {i}: {story.title}")
                content.append(f"**Goal**: {story.description}")

                # Generate workflow steps based on project type
                if inference_result.project_type.value == "rest_api":
                    content.append(self._generate_api_workflow(story, inference_result))
                elif inference_result.project_type.value == "cli_tool":
                    content.append(self._generate_cli_workflow(story, inference_result))
                elif inference_result.project_type.value == "web_application":
                    content.append(self._generate_web_workflow(story, inference_result))
                else:
                    content.append(self._generate_generic_workflow(story, inference_result))

        else:
            # Generate default workflows based on functional requirements
            content.append("\n### Basic Operations")

            for fr in inference_result.functional_requirements[:3]:
                content.append(f"\n**{fr.title}**:")
                content.append(f"- Purpose: {fr.description}")

                if fr.api_endpoints:
                    content.append("- API Endpoints:")
                    for endpoint in fr.api_endpoints[:2]:
                        content.append(f"  - `{endpoint}`")

        return "\n".join(content)

    def _generate_api_workflow(self, story: UserStory, inference_result: InferenceResult) -> str:
        """Generate API workflow for user story."""

        workflow = []

        # Determine operation type from story
        story_lower = story.title.lower()

        if 'create' in story_lower:
            workflow.append("\n**API Request**:")
            workflow.append("```bash")
            workflow.append("curl -X POST http://localhost:8000/api/resource \\")
            workflow.append("  -H \"Content-Type: application/json\" \\")
            workflow.append("  -d '{\"name\": \"example\", \"description\": \"test data\"}'")
            workflow.append("```")

            workflow.append("\n**Expected Response**:")
            workflow.append("```json")
            workflow.append("{")
            workflow.append("  \"id\": 1,")
            workflow.append("  \"name\": \"example\",")
            workflow.append("  \"description\": \"test data\",")
            workflow.append("  \"created_at\": \"2024-01-01T12:00:00Z\"")
            workflow.append("}")
            workflow.append("```")

        elif 'view' in story_lower or 'get' in story_lower:
            workflow.append("\n**API Request**:")
            workflow.append("```bash")
            workflow.append("curl http://localhost:8000/api/resource/1")
            workflow.append("```")

            workflow.append("\n**Expected Response**:")
            workflow.append("```json")
            workflow.append("{")
            workflow.append("  \"id\": 1,")
            workflow.append("  \"name\": \"example\",")
            workflow.append("  \"status\": \"active\"")
            workflow.append("}")
            workflow.append("```")

        elif 'update' in story_lower:
            workflow.append("\n**API Request**:")
            workflow.append("```bash")
            workflow.append("curl -X PUT http://localhost:8000/api/resource/1 \\")
            workflow.append("  -H \"Content-Type: application/json\" \\")
            workflow.append("  -d '{\"name\": \"updated name\"}'")
            workflow.append("```")

        elif 'delete' in story_lower:
            workflow.append("\n**API Request**:")
            workflow.append("```bash")
            workflow.append("curl -X DELETE http://localhost:8000/api/resource/1")
            workflow.append("```")

        return "\n".join(workflow)

    def _generate_cli_workflow(self, story: UserStory, inference_result: InferenceResult) -> str:
        """Generate CLI workflow for user story."""

        workflow = []

        story_lower = story.title.lower()

        if 'create' in story_lower:
            workflow.append("\n**Command**:")
            workflow.append("```bash")
            workflow.append("./cli create --name \"example\" --description \"test data\"")
            workflow.append("```")

            workflow.append("\n**Expected Output**:")
            workflow.append("```")
            workflow.append("✓ Created successfully")
            workflow.append("ID: 123")
            workflow.append("Name: example")
            workflow.append("```")

        elif 'view' in story_lower or 'list' in story_lower:
            workflow.append("\n**Command**:")
            workflow.append("```bash")
            workflow.append("./cli list")
            workflow.append("# OR")
            workflow.append("./cli show 123")
            workflow.append("```")

            workflow.append("\n**Expected Output**:")
            workflow.append("```")
            workflow.append("ID   Name      Status")
            workflow.append("123  example   active")
            workflow.append("124  test      pending")
            workflow.append("```")

        elif 'update' in story_lower:
            workflow.append("\n**Command**:")
            workflow.append("```bash")
            workflow.append("./cli update 123 --name \"updated name\"")
            workflow.append("```")

        elif 'delete' in story_lower:
            workflow.append("\n**Command**:")
            workflow.append("```bash")
            workflow.append("./cli delete 123")
            workflow.append("```")

        return "\n".join(workflow)

    def _generate_web_workflow(self, story: UserStory, inference_result: InferenceResult) -> str:
        """Generate web workflow for user story."""

        workflow = []

        workflow.append("\n**Browser Steps**:")
        workflow.append("1. Open http://localhost:3000 in your browser")

        story_lower = story.title.lower()

        if 'create' in story_lower:
            workflow.append("2. Click 'Create New' button")
            workflow.append("3. Fill in the form fields")
            workflow.append("4. Click 'Submit'")
            workflow.append("5. Verify success message appears")

        elif 'view' in story_lower:
            workflow.append("2. Navigate to the main dashboard")
            workflow.append("3. Browse through the list of items")
            workflow.append("4. Click on an item to view details")

        elif 'update' in story_lower:
            workflow.append("2. Find the item you want to update")
            workflow.append("3. Click 'Edit' button")
            workflow.append("4. Modify the fields")
            workflow.append("5. Click 'Save Changes'")

        elif 'delete' in story_lower:
            workflow.append("2. Find the item you want to delete")
            workflow.append("3. Click 'Delete' button")
            workflow.append("4. Confirm deletion in the popup")

        workflow.append("\n**Success Criteria**:")
        for criterion in story.acceptance_criteria[:2]:
            workflow.append(f"- {criterion}")

        return "\n".join(workflow)

    def _generate_generic_workflow(self, story: UserStory, inference_result: InferenceResult) -> str:
        """Generate generic workflow for user story."""

        workflow = []

        workflow.append("\n**Steps**:")
        workflow.append("1. Follow application-specific procedures")
        workflow.append("2. Use appropriate interface (API, CLI, or Web)")
        workflow.append("3. Verify expected outcomes")

        workflow.append("\n**Success Criteria**:")
        for criterion in story.acceptance_criteria[:3]:
            workflow.append(f"- {criterion}")

        return "\n".join(workflow)

    def _generate_testing_guide(self, inference_result: InferenceResult) -> str:
        """Generate testing guide section."""

        content = ["## Testing Guide"]

        # Unit tests
        content.append("\n### Run Unit Tests")
        content.append("```bash")

        # Test commands based on detected technology
        python_detected = any("python" in decision.title.lower() for decision in inference_result.technical_decisions)
        node_detected = any("javascript" in decision.title.lower() or "node" in decision.title.lower()
                           for decision in inference_result.technical_decisions)
        go_detected = any("go" in decision.title.lower() for decision in inference_result.technical_decisions)

        if python_detected:
            content.append("# Python")
            content.append("pytest")
            content.append("# OR")
            content.append("python -m pytest tests/")
        elif node_detected:
            content.append("# Node.js")
            content.append("npm test")
            content.append("# OR")
            content.append("npm run test:unit")
        elif go_detected:
            content.append("# Go")
            content.append("go test ./...")
        else:
            content.append("# Run test suite")
            content.append("# Check project documentation for specific test commands")

        content.append("```")

        # Integration tests
        content.append("\n### Run Integration Tests")
        content.append("```bash")

        if python_detected:
            content.append("pytest tests/integration/")
        elif node_detected:
            content.append("npm run test:integration")
        elif go_detected:
            content.append("go test -tags=integration ./...")
        else:
            content.append("# Run integration test suite")

        content.append("```")

        # API tests (if applicable)
        if inference_result.project_type.value in ["rest_api", "web_application"]:
            content.append("\n### API Testing")
            content.append("```bash")
            content.append("# Test all endpoints")
            content.append("curl http://localhost:8000/health")
            content.append("")
            content.append("# Run API test suite")
            if python_detected:
                content.append("pytest tests/api/")
            elif node_detected:
                content.append("npm run test:api")
            else:
                content.append("# Run API-specific tests")
            content.append("```")

        # Test coverage
        content.append("\n### Test Coverage")
        content.append("```bash")

        if python_detected:
            content.append("pytest --cov=src tests/")
            content.append("# Generate HTML report")
            content.append("pytest --cov=src --cov-report=html tests/")
        elif node_detected:
            content.append("npm run test:coverage")
        elif go_detected:
            content.append("go test -cover ./...")
        else:
            content.append("# Run tests with coverage reporting")

        content.append("```")

        return "\n".join(content)

    def _generate_troubleshooting(self, inference_result: InferenceResult) -> str:
        """Generate troubleshooting section."""

        content = ["## Troubleshooting"]

        # Common issues
        content.append("\n### Common Issues")

        # Database connection issues
        if any("database" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("\n**Database Connection Issues**:")
            content.append("```bash")
            content.append("# Check database is running")
            content.append("docker ps | grep database")
            content.append("")
            content.append("# Check connection string in .env")
            content.append("cat .env | grep DATABASE")
            content.append("")
            content.append("# Test database connection")
            content.append("# Python: python -c \"import psycopg2; print('OK')\"")
            content.append("# Node.js: node -e \"console.log('Test DB connection')\"")
            content.append("```")

        # Port conflicts
        if inference_result.project_type.value in ["rest_api", "web_application"]:
            content.append("\n**Port Already in Use**:")
            content.append("```bash")
            content.append("# Find process using port 8000")
            content.append("lsof -i :8000")
            content.append("# OR on Windows")
            content.append("netstat -ano | findstr :8000")
            content.append("")
            content.append("# Kill process")
            content.append("kill -9 <PID>")
            content.append("```")

        # Dependencies issues
        content.append("\n**Dependency Issues**:")
        content.append("```bash")

        if any("python" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("# Python - Clear cache and reinstall")
            content.append("pip cache purge")
            content.append("pip install -r requirements.txt --force-reinstall")
        elif any("node" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("# Node.js - Clear cache and reinstall")
            content.append("npm cache clean --force")
            content.append("rm -rf node_modules")
            content.append("npm install")
        else:
            content.append("# Clear dependency cache and reinstall")
            content.append("# Check project-specific instructions")

        content.append("```")

        # Environment variables
        content.append("\n**Environment Configuration**:")
        content.append("```bash")
        content.append("# Verify environment file exists")
        content.append("ls -la .env*")
        content.append("")
        content.append("# Check required variables are set")
        content.append("cat .env | grep -E \"DATABASE|API_KEY|SECRET\"")
        content.append("```")

        # External services
        if inference_result.integrations:
            content.append("\n**External Service Issues**:")
            content.append("- Verify API credentials are correct")
            content.append("- Check service status pages")
            content.append("- Test connectivity with simple requests")
            content.append("- Review rate limiting and quotas")

        # Debug mode
        content.append("\n### Debug Mode")
        content.append("```bash")
        content.append("# Enable debug logging")
        content.append("export DEBUG=1")
        content.append("# OR set in .env file")
        content.append("echo \"DEBUG=true\" >> .env")
        content.append("")
        content.append("# Run with verbose output")
        if any("python" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("python -v app.py")
        elif any("node" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("NODE_ENV=development npm start")
        else:
            content.append("# Use framework-specific debug mode")
        content.append("```")

        return "\n".join(content)

    def _generate_performance_validation(self, inference_result: InferenceResult) -> str:
        """Generate performance validation section."""

        content = ["## Performance Validation"]

        if inference_result.performance_requirements:
            content.append("\n### Performance Requirements Check")
            for req in inference_result.performance_requirements:
                content.append(f"- {req}")

        # Load testing
        if inference_result.project_type.value in ["rest_api", "web_application"]:
            content.append("\n### Basic Load Testing")
            content.append("```bash")
            content.append("# Install load testing tool")
            content.append("pip install locust")
            content.append("# OR")
            content.append("npm install -g artillery")
            content.append("")
            content.append("# Basic load test")
            content.append("curl -w \"@curl-format.txt\" http://localhost:8000/api/health")
            content.append("")
            content.append("# Curl format file (curl-format.txt):")
            content.append("echo 'time_total: %{time_total}\\ntime_connect: %{time_connect}\\n' > curl-format.txt")
            content.append("```")

        # Memory usage
        content.append("\n### Memory Usage Check")
        content.append("```bash")
        content.append("# Monitor memory usage")
        if any("python" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("pip install memory-profiler")
            content.append("python -m memory_profiler app.py")
        elif any("node" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("node --inspect app.js")
            content.append("# Use Chrome DevTools for memory profiling")
        else:
            content.append("# Use system monitoring tools")
            content.append("top -p $(pgrep -f 'your-app')")

        content.append("```")

        # Database performance
        if any("database" in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append("\n### Database Performance")
            content.append("```bash")
            content.append("# Check slow queries")
            content.append("# PostgreSQL:")
            content.append("# SELECT query, calls, total_time FROM pg_stat_statements ORDER BY total_time DESC;")
            content.append("")
            content.append("# MySQL:")
            content.append("# SHOW FULL PROCESSLIST;")
            content.append("```")

        return "\n".join(content)

    def _generate_next_steps(self, inference_result: InferenceResult) -> str:
        """Generate next steps section."""

        content = ["## Next Steps"]

        # Development workflow
        content.append("\n### Development Workflow")
        content.append("1. **Read the Documentation**: Review spec.md and plan.md for detailed requirements")
        content.append("2. **Review the Tasks**: Check tasks.md for implementation breakdown")
        content.append("3. **Setup Development Environment**: Configure IDE, linting, and debugging tools")
        content.append("4. **Start with Tests**: Follow TDD approach by implementing tests first")

        # Production preparation
        content.append("\n### Production Preparation")
        content.append("1. **Environment Configuration**: Set up production environment variables")
        content.append("2. **Security Review**: Implement security measures from the security considerations")
        content.append("3. **Performance Testing**: Validate performance under realistic load")
        content.append("4. **Monitoring Setup**: Configure logging, metrics, and alerting")

        # Integration tasks
        if inference_result.integrations:
            content.append("\n### Integration Setup")
            content.append("1. **External Services**: Configure production API keys and credentials")
            content.append("2. **Service Dependencies**: Set up and configure external service connections")
            content.append("3. **Fallback Strategies**: Implement circuit breakers and error handling")

        # Deployment
        content.append("\n### Deployment")
        content.append("1. **Containerization**: Build and test Docker containers")
        content.append("2. **CI/CD Pipeline**: Set up automated testing and deployment")
        content.append("3. **Infrastructure**: Provision production infrastructure")
        content.append("4. **Monitoring**: Deploy monitoring and observability tools")

        # Documentation
        content.append("\n### Documentation")
        content.append("1. **API Documentation**: Generate and publish API documentation")
        content.append("2. **User Guides**: Create end-user documentation")
        content.append("3. **Deployment Guides**: Document deployment and operations procedures")
        content.append("4. **Troubleshooting**: Expand troubleshooting guides based on common issues")

        # Learning resources
        content.append("\n### Learning Resources")
        for decision in inference_result.technical_decisions[:3]:
            framework = decision.title.split(":")[-1].strip() if ":" in decision.title else decision.title
            content.append(f"- **{framework}**: Review official documentation and best practices")

        content.append("- **Architecture Patterns**: Study the chosen architecture pattern in depth")
        if inference_result.security_considerations:
            content.append("- **Security**: Review OWASP guidelines and security best practices")

        return "\n".join(content)