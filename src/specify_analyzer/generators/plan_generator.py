"""Generator for plan.md files from code analysis results."""

from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from ..inference import InferenceResult, TechnicalDecision


class PlanGenerator:
    """Generates implementation plan documents from inference results."""

    def __init__(self):
        self.template_sections = [
            "summary",
            "technical_context",
            "constitution_check",
            "project_structure",
            "phase_0_research",
            "phase_1_design",
            "phase_2_planning",
            "complexity_tracking",
            "progress_tracking"
        ]

    def generate_plan_md(self, inference_result: InferenceResult, project_name: str) -> str:
        """Generate complete plan.md content from inference results."""

        sections = []

        # Header
        sections.append(self._generate_header(project_name, inference_result))

        # Summary
        sections.append(self._generate_summary(inference_result))

        # Technical Context
        sections.append(self._generate_technical_context(inference_result))

        # Constitution Check
        sections.append(self._generate_constitution_check(inference_result))

        # Project Structure
        sections.append(self._generate_project_structure(inference_result))

        # Phase 0: Research
        sections.append(self._generate_phase_0_research(inference_result))

        # Phase 1: Design & Contracts
        sections.append(self._generate_phase_1_design(inference_result))

        # Phase 2: Task Planning
        sections.append(self._generate_phase_2_planning(inference_result))

        # Complexity Tracking
        sections.append(self._generate_complexity_tracking(inference_result))

        # Progress Tracking
        sections.append(self._generate_progress_tracking(inference_result))

        return "\n\n".join(sections)

    def _generate_header(self, project_name: str, inference_result: InferenceResult) -> str:
        """Generate document header."""

        today = datetime.now().strftime("%Y-%m-%d")
        branch_name = f"feature-{project_name.lower().replace(' ', '-')}"

        return f"""---
description: \"Implementation plan for {project_name} feature development\"
scripts:
  sh: scripts/bash/update-agent-context.sh __AGENT__
  ps: scripts/powershell/update-agent-context.ps1 -AgentType __AGENT__
---

# Implementation Plan: {project_name}

**Branch**: `{branch_name}` | **Date**: {today} | **Spec**: [link]
**Input**: Feature specification from analysis of existing codebase

## Execution Flow (/plan command scope)
```
1. Load feature analysis from code scanning results
   → Project type: {inference_result.project_type.value}
   → Architecture: {inference_result.architecture.value}
2. Fill Technical Context based on detected technologies
3. Evaluate Constitution Check section
4. Execute Phase 0 → research.md (technical decision analysis)
5. Execute Phase 1 → contracts, data-model.md, quickstart.md, agent file
6. Re-evaluate Constitution Check section
7. Plan Phase 2 → Describe task generation approach
8. STOP - Ready for /tasks command
```

**IMPORTANT**: The /plan command STOPS at step 7. Implementation phases follow later."""

    def _generate_summary(self, inference_result: InferenceResult) -> str:
        """Generate summary section."""

        primary_tech = "Multiple technologies"
        if inference_result.technical_decisions:
            primary_tech = inference_result.technical_decisions[0].title

        return f"""## Summary
{inference_result.primary_purpose}

**Technical Approach**: {primary_tech} with {inference_result.architecture.value} architecture pattern, targeting {', '.join(inference_result.target_users[:2])}."""

    def _generate_technical_context(self, inference_result: InferenceResult) -> str:
        """Generate technical context section."""

        # Extract technology info from technical decisions
        language = "NEEDS CLARIFICATION"
        dependencies = "NEEDS CLARIFICATION"
        storage = "N/A"
        testing = "NEEDS CLARIFICATION"
        platform = "NEEDS CLARIFICATION"
        performance_goals = "NEEDS CLARIFICATION"
        constraints = "NEEDS CLARIFICATION"
        scale = "NEEDS CLARIFICATION"

        # Try to extract info from technical decisions
        for decision in inference_result.technical_decisions:
            if "language" in decision.title.lower():
                language = decision.title.split(":")[-1].strip()
            elif "framework" in decision.title.lower():
                dependencies = decision.title.split(":")[-1].strip()
            elif "database" in decision.title.lower():
                storage = decision.title.split(":")[-1].strip()

        # Infer project type details
        project_type = "single"
        if inference_result.project_type.value == "web_application":
            project_type = "web"
        elif inference_result.project_type.value == "mobile_app":
            project_type = "mobile"

        # Set performance goals based on project type
        if inference_result.project_type.value == "rest_api":
            performance_goals = "1000 req/s response time"
            constraints = "<200ms p95 latency"
            scale = "10k+ concurrent users"
        elif inference_result.project_type.value == "cli_tool":
            performance_goals = "< 1s command execution"
            constraints = "< 100MB memory usage"
            scale = "single user operations"

        return f"""## Technical Context
**Language/Version**: {language}
**Primary Dependencies**: {dependencies}
**Storage**: {storage}
**Testing**: {testing}
**Target Platform**: {platform}
**Project Type**: {project_type}
**Performance Goals**: {performance_goals}
**Constraints**: {constraints}
**Scale/Scope**: {scale}"""

    def _generate_constitution_check(self, inference_result: InferenceResult) -> str:
        """Generate constitution check section."""

        checks = []

        # Complexity check based on number of entities
        entity_count = len(inference_result.key_entities)
        if entity_count <= 3:
            checks.append("✅ **Entity Complexity**: ≤3 core entities - PASS")
        else:
            checks.append(f"⚠️ **Entity Complexity**: {entity_count} entities (>3) - NEEDS JUSTIFICATION")

        # Integration complexity
        integration_count = len(inference_result.integrations)
        if integration_count <= 2:
            checks.append("✅ **Integration Complexity**: ≤2 external services - PASS")
        else:
            checks.append(f"⚠️ **Integration Complexity**: {integration_count} services (>2) - NEEDS JUSTIFICATION")

        # Architecture complexity
        if inference_result.architecture.value in ["monolithic", "layered", "mvc"]:
            checks.append("✅ **Architecture Complexity**: Simple pattern - PASS")
        else:
            checks.append(f"⚠️ **Architecture Complexity**: {inference_result.architecture.value} - NEEDS JUSTIFICATION")

        # NFR complexity
        nfr_count = len(inference_result.non_functional_requirements)
        if nfr_count <= 4:
            checks.append("✅ **NFR Complexity**: ≤4 categories - PASS")
        else:
            checks.append(f"⚠️ **NFR Complexity**: {nfr_count} categories (>4) - NEEDS JUSTIFICATION")

        checks_content = "\n".join(checks)

        return f"""## Constitution Check
*GATE: Must pass before Phase 0 research. Re-check after Phase 1 design.*

{checks_content}

**Overall Status**: {"PASS" if "⚠️" not in checks_content else "NEEDS REVIEW"}"""

    def _generate_project_structure(self, inference_result: InferenceResult) -> str:
        """Generate project structure section."""

        # Documentation structure
        doc_structure = """### Documentation (this feature)
```
specs/feature/
├── plan.md              # This file (/plan command output)
├── research.md          # Phase 0 output (/plan command)
├── data-model.md        # Phase 1 output (/plan command)
├── quickstart.md        # Phase 1 output (/plan command)
├── contracts/           # Phase 1 output (/plan command)
└── tasks.md             # Phase 2 output (/tasks command - NOT created by /plan)
```"""

        # Source structure based on project type
        if inference_result.project_type.value == "web_application":
            source_structure = """### Source Code (repository root)
```
# Option 2: Web application
backend/
├── src/
│   ├── models/
│   ├── services/
│   └── api/
└── tests/

frontend/
├── src/
│   ├── components/
│   ├── pages/
│   └── services/
└── tests/
```

**Structure Decision**: Web application with separate frontend/backend"""

        elif inference_result.project_type.value == "mobile_app":
            source_structure = """### Source Code (repository root)
```
# Option 3: Mobile + API
api/
├── src/
│   ├── models/
│   ├── services/
│   └── api/
└── tests/

mobile/
├── src/
│   ├── screens/
│   ├── components/
│   └── services/
└── tests/
```

**Structure Decision**: Mobile application with API backend"""

        else:
            # Default single project structure
            source_structure = f"""### Source Code (repository root)
```
# Option 1: Single project (DEFAULT)
src/
├── models/
├── services/
├── {self._get_main_directory(inference_result)}/
└── lib/

tests/
├── contract/
├── integration/
└── unit/
```

**Structure Decision**: Single project structure for {inference_result.project_type.value}"""

        return f"""## Project Structure

{doc_structure}

{source_structure}"""

    def _generate_phase_0_research(self, inference_result: InferenceResult) -> str:
        """Generate Phase 0 research section."""

        research_tasks = []

        # Research unknown technologies
        research_tasks.append("1. **Validate detected technologies**:")
        for decision in inference_result.technical_decisions[:3]:
            research_tasks.append(f"   - Research {decision.title} implementation patterns")

        # Research integrations
        if inference_result.integrations:
            research_tasks.append("2. **External integrations research**:")
            for integration in inference_result.integrations[:3]:
                research_tasks.append(f"   - {integration.title()} API documentation and best practices")

        # Research architecture patterns
        research_tasks.append("3. **Architecture pattern research**:")
        research_tasks.append(f"   - {inference_result.architecture.value.title()} architecture implementation")
        research_tasks.append(f"   - Best practices for {inference_result.project_type.value}")

        # Research performance requirements
        if inference_result.performance_requirements:
            research_tasks.append("4. **Performance requirements research**:")
            for req in inference_result.performance_requirements[:2]:
                research_tasks.append(f"   - {req}")

        research_content = "\n".join(research_tasks)

        return f"""## Phase 0: Outline & Research
{research_content}

**Consolidation**: Update Technical Context with research findings, replacing all NEEDS CLARIFICATION items.

**Output**: research.md with technology decisions, architecture rationale, and implementation approaches."""

    def _generate_phase_1_design(self, inference_result: InferenceResult) -> str:
        """Generate Phase 1 design section."""

        # Data model extraction
        entities_section = ""
        if inference_result.key_entities:
            entities_section = f"""1. **Extract entities from analysis** → `data-model.md`:
   - Identified entities: {', '.join(inference_result.key_entities[:5])}
   - Entity relationships and validation rules
   - State transitions if applicable"""
        else:
            entities_section = """1. **Define core entities** → `data-model.md`:
   - Extract entities from functional requirements
   - Define relationships and constraints"""

        # API contracts
        api_section = ""
        if inference_result.project_type.value in ["rest_api", "web_application", "microservice"]:
            api_section = """2. **Generate API contracts** from functional requirements:
   - RESTful endpoint definitions
   - Request/response schemas
   - OpenAPI specification to `/contracts/`"""
        elif inference_result.project_type.value == "cli_tool":
            api_section = """2. **Generate CLI contracts** from functional requirements:
   - Command definitions and arguments
   - Input/output specifications
   - Help text and usage examples"""
        else:
            api_section = """2. **Generate interface contracts**:
   - Define public interfaces
   - Input/output specifications
   - Integration contracts"""

        # Test generation
        test_section = """3. **Generate contract tests** from contracts:
   - One test file per endpoint/command
   - Assert request/response schemas
   - Tests must fail (no implementation yet)"""

        # User story tests
        story_section = """4. **Extract test scenarios** from user stories:
   - Each story → integration test scenario
   - Quickstart test = story validation steps"""

        # Agent file update
        agent_section = """5. **Update agent file incrementally** (O(1) operation):
   - Run script specified in header
   - Add NEW tech from current plan
   - Preserve manual additions between markers
   - Keep under 150 lines for token efficiency"""

        return f"""## Phase 1: Design & Contracts
*Prerequisites: research.md complete*

{entities_section}

{api_section}

{test_section}

{story_section}

{agent_section}

**Output**: data-model.md, /contracts/*, failing tests, quickstart.md, agent-specific file"""

    def _generate_phase_2_planning(self, inference_result: InferenceResult) -> str:
        """Generate Phase 2 planning section."""

        # Estimate task count based on complexity
        functional_req_count = len(inference_result.functional_requirements)
        entity_count = len(inference_result.key_entities)
        estimated_tasks = max(15, functional_req_count * 3 + entity_count * 2)

        return f"""## Phase 2: Task Planning Approach
*This section describes what the /tasks command will do - DO NOT execute during /plan*

**Task Generation Strategy**:
- Load `.specify/templates/tasks-template.md` as base
- Generate tasks from Phase 1 design docs (contracts, data model, quickstart)
- Each contract → contract test task [P]
- Each entity → model creation task [P]
- Each user story → integration test task
- Implementation tasks to make tests pass

**Ordering Strategy**:
- TDD order: Tests before implementation
- Dependency order: Models before services before {self._get_main_directory(inference_result)}
- Mark [P] for parallel execution (independent files)

**Estimated Output**: {estimated_tasks}-{estimated_tasks + 10} numbered, ordered tasks in tasks.md

**IMPORTANT**: This phase is executed by the /tasks command, NOT by /plan"""

    def _generate_complexity_tracking(self, inference_result: InferenceResult) -> str:
        """Generate complexity tracking section."""

        # Check for complexity violations
        violations = []

        entity_count = len(inference_result.key_entities)
        if entity_count > 3:
            violations.append({
                'violation': f'{entity_count} entities (>3)',
                'why_needed': 'Core domain complexity requires multiple entities',
                'alternative': f'Simpler approach with ≤3 entities insufficient for domain model'
            })

        integration_count = len(inference_result.integrations)
        if integration_count > 2:
            violations.append({
                'violation': f'{integration_count} external services (>2)',
                'why_needed': 'Business requirements mandate multiple integrations',
                'alternative': 'Fewer integrations would compromise functionality'
            })

        if inference_result.architecture.value not in ["monolithic", "layered", "mvc"]:
            violations.append({
                'violation': f'{inference_result.architecture.value} architecture',
                'why_needed': 'Scale and complexity requirements',
                'alternative': 'Simpler architecture insufficient for requirements'
            })

        if violations:
            table_rows = []
            for v in violations:
                table_rows.append(f"| {v['violation']} | {v['why_needed']} | {v['alternative']} |")

            violation_table = "\n".join(table_rows)

            return f"""## Complexity Tracking
*Fill ONLY if Constitution Check has violations that must be justified*

| Violation | Why Needed | Simpler Alternative Rejected Because |
|-----------|------------|-------------------------------------|
{violation_table}"""
        else:
            return """## Complexity Tracking
*Fill ONLY if Constitution Check has violations that must be justified*

| Violation | Why Needed | Simpler Alternative Rejected Because |
|-----------|------------|-------------------------------------|
| No violations | N/A | N/A |"""

    def _generate_progress_tracking(self, inference_result: InferenceResult) -> str:
        """Generate progress tracking section."""

        return """## Progress Tracking
*This checklist is updated during execution flow*

**Phase Status**:
- [ ] Phase 0: Research complete (/plan command)
- [ ] Phase 1: Design complete (/plan command)
- [ ] Phase 2: Task planning complete (/plan command - describe approach only)
- [ ] Phase 3: Tasks generated (/tasks command)
- [ ] Phase 4: Implementation complete
- [ ] Phase 5: Validation passed

**Gate Status**:
- [ ] Initial Constitution Check: PASS
- [ ] Post-Design Constitution Check: PASS
- [ ] All NEEDS CLARIFICATION resolved
- [ ] Complexity deviations documented

---
*Based on Constitution v2.1.1 - See `/memory/constitution.md`*"""

    def _get_main_directory(self, inference_result: InferenceResult) -> str:
        """Get the main directory name based on project type."""

        if inference_result.project_type.value == "cli_tool":
            return "cli"
        elif inference_result.project_type.value == "rest_api":
            return "api"
        elif inference_result.project_type.value == "web_application":
            return "web"
        elif inference_result.project_type.value == "library":
            return "lib"
        else:
            return "core"