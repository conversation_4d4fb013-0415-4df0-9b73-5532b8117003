"""Generator for spec.md files from code analysis results."""

from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from ..inference import InferenceResult, FunctionalRequirement, NonFunctionalRequirement, UserStory


class SpecGenerator:
    """Generates specification documents from inference results."""

    def __init__(self):
        self.template_sections = [
            "project_vision",
            "functional_requirements",
            "non_functional_requirements",
            "user_scenarios",
            "interface_specifications",
            "integration_requirements",
            "data_requirements",
            "compliance_constraints",
            "success_metrics",
            "assumptions_dependencies"
        ]

    def generate_spec_md(self, inference_result: InferenceResult, project_name: str) -> str:
        """Generate complete spec.md content from inference results."""

        sections = []

        # Header
        sections.append(self._generate_header(project_name, inference_result))

        # Project Vision
        sections.append(self._generate_project_vision(inference_result))

        # Functional Requirements
        sections.append(self._generate_functional_requirements(inference_result))

        # Non-Functional Requirements
        sections.append(self._generate_non_functional_requirements(inference_result))

        # User Scenarios & Testing
        sections.append(self._generate_user_scenarios(inference_result))

        # Interface Specifications
        sections.append(self._generate_interface_specifications(inference_result))

        # Integration Requirements
        sections.append(self._generate_integration_requirements(inference_result))

        # Data Requirements
        sections.append(self._generate_data_requirements(inference_result))

        # Compliance and Constraints
        sections.append(self._generate_compliance_constraints(inference_result))

        # Success Metrics
        sections.append(self._generate_success_metrics(inference_result))

        # Assumptions and Dependencies
        sections.append(self._generate_assumptions_dependencies(inference_result))

        return "\n\n".join(sections)

    def _generate_header(self, project_name: str, inference_result: InferenceResult) -> str:
        """Generate document header with project metadata."""

        return f"""# Specification - {project_name}

## Project Vision

### Purpose Statement
{inference_result.primary_purpose}

This {inference_result.project_type.value.replace('_', ' ')} follows a {inference_result.architecture.value} architecture pattern to deliver functionality to {', '.join(inference_result.target_users[:3])}.

### Core Value Proposition
- **Primary Function**: {self._extract_core_function(inference_result)}
- **Target Users**: {', '.join(inference_result.target_users)}
- **Key Benefits**: {self._extract_key_benefits(inference_result)}
- **Architecture**: {inference_result.architecture.value.title().replace('_', ' ')} design pattern"""

    def _generate_project_vision(self, inference_result: InferenceResult) -> str:
        """Generate project vision section."""

        vision_content = []

        # Core capabilities from functional requirements
        capabilities = []
        for fr in inference_result.functional_requirements[:5]:  # Top 5 capabilities
            capabilities.append(f"- **{fr.title}**: {fr.description}")

        if capabilities:
            vision_content.append("### Core Capabilities")
            vision_content.extend(capabilities)

        # Technical approach
        tech_decisions = inference_result.technical_decisions[:3]  # Top 3 decisions
        if tech_decisions:
            vision_content.append("\n### Technical Approach")
            for decision in tech_decisions:
                vision_content.append(f"- **{decision.title}**: {decision.description}")

        return "\n".join(vision_content) if vision_content else "### Core Capabilities\n- Functionality extracted from code analysis"

    def _generate_functional_requirements(self, inference_result: InferenceResult) -> str:
        """Generate functional requirements section."""

        content = ["## Functional Requirements"]

        for i, fr in enumerate(inference_result.functional_requirements, 1):
            content.append(f"\n### {fr.id}: {fr.title}")
            content.append(f"**Description**: {fr.description}")

            # Acceptance Criteria
            if fr.acceptance_criteria:
                content.append("\n**Acceptance Criteria**:")
                for criterion in fr.acceptance_criteria:
                    content.append(f"- {criterion}")

            # User Stories (if available)
            related_stories = [story for story in inference_result.user_stories
                             if any(model in story.related_models for model in fr.data_models)]
            if related_stories:
                content.append("\n**User Stories**:")
                for story in related_stories[:2]:  # Limit to 2 stories per requirement
                    content.append(f"- {story.description}")

            # API Endpoints (if available)
            if fr.api_endpoints:
                content.append("\n**API Endpoints**:")
                for endpoint in fr.api_endpoints:
                    content.append(f"- `{endpoint}`")

            # Dependencies
            if fr.dependencies:
                content.append("\n**Dependencies**:")
                for dep in fr.dependencies:
                    content.append(f"- {dep}")

        return "\n".join(content)

    def _generate_non_functional_requirements(self, inference_result: InferenceResult) -> str:
        """Generate non-functional requirements section."""

        content = ["## Non-Functional Requirements"]

        # Group NFRs by category
        nfr_categories = {}
        for nfr in inference_result.non_functional_requirements:
            category = nfr.category
            if category not in nfr_categories:
                nfr_categories[category] = []
            nfr_categories[category].append(nfr)

        for category, nfrs in nfr_categories.items():
            content.append(f"\n### NFR-{category.upper()}: {category}")

            for nfr in nfrs:
                content.append(f"- {nfr.requirement}")
                if nfr.description:
                    content.append(f"  - {nfr.description}")

                # Metrics
                if nfr.metrics:
                    content.append("  - **Metrics**:")
                    for metric in nfr.metrics[:3]:  # Limit to 3 metrics
                        content.append(f"    - {metric}")

                # Constraints
                if nfr.constraints:
                    content.append("  - **Constraints**:")
                    for constraint in nfr.constraints[:3]:  # Limit to 3 constraints
                        content.append(f"    - {constraint}")

        return "\n".join(content)

    def _generate_user_scenarios(self, inference_result: InferenceResult) -> str:
        """Generate user scenarios and testing section."""

        content = ["## User Scenarios & Testing"]

        # Primary user scenarios from user stories
        content.append("\n### Primary User Scenarios")

        for i, story in enumerate(inference_result.user_stories[:5], 1):  # Top 5 scenarios
            content.append(f"\n#### Scenario {i}: {story.title}")
            content.append(f"**Description**: {story.description}")

            if story.acceptance_criteria:
                content.append("\n**Acceptance Criteria**:")
                for criterion in story.acceptance_criteria:
                    content.append(f"- {criterion}")

            content.append(f"\n**Priority**: {story.priority}")
            content.append(f"**Estimated Effort**: {story.estimated_effort}")

        # Testing approach
        content.append("\n### Testing Strategy")
        content.append("- **Unit Testing**: Test individual components and functions")
        content.append("- **Integration Testing**: Test component interactions")
        content.append("- **API Testing**: Validate endpoint functionality and responses")

        if inference_result.security_considerations:
            content.append("- **Security Testing**: Validate authentication and authorization")

        if inference_result.performance_requirements:
            content.append("- **Performance Testing**: Validate response times and throughput")

        return "\n".join(content)

    def _generate_interface_specifications(self, inference_result: InferenceResult) -> str:
        """Generate interface specifications section."""

        content = ["## Interface Specifications"]

        # Determine interface type based on project type
        if inference_result.project_type.value in ['rest_api', 'microservice']:
            content.append("\n### REST API Interface")

            # Group user stories by HTTP method patterns
            get_stories = [s for s in inference_result.user_stories if 'view' in s.title.lower() or 'get' in s.title.lower()]
            post_stories = [s for s in inference_result.user_stories if 'create' in s.title.lower()]
            put_stories = [s for s in inference_result.user_stories if 'update' in s.title.lower()]
            delete_stories = [s for s in inference_result.user_stories if 'delete' in s.title.lower()]

            if get_stories:
                content.append("\n#### GET Endpoints")
                for story in get_stories[:3]:
                    endpoint_path = self._infer_endpoint_path(story)
                    content.append(f"- `GET {endpoint_path}` - {story.description}")

            if post_stories:
                content.append("\n#### POST Endpoints")
                for story in post_stories[:3]:
                    endpoint_path = self._infer_endpoint_path(story)
                    content.append(f"- `POST {endpoint_path}` - {story.description}")

            if put_stories:
                content.append("\n#### PUT Endpoints")
                for story in put_stories[:3]:
                    endpoint_path = self._infer_endpoint_path(story)
                    content.append(f"- `PUT {endpoint_path}` - {story.description}")

            if delete_stories:
                content.append("\n#### DELETE Endpoints")
                for story in delete_stories[:3]:
                    endpoint_path = self._infer_endpoint_path(story)
                    content.append(f"- `DELETE {endpoint_path}` - {story.description}")

        elif inference_result.project_type.value == 'cli_tool':
            content.append("\n### Command Line Interface")
            content.append("```bash")

            # Infer commands from functional requirements
            for fr in inference_result.functional_requirements[:5]:
                command = self._infer_cli_command(fr)
                content.append(f"{command}")

            content.append("```")

        elif inference_result.project_type.value == 'web_application':
            content.append("\n### Web Interface")
            content.append("- **Frontend**: User interface for web browsers")
            content.append("- **Backend API**: Server-side functionality")
            content.append("- **Authentication**: User login and session management")

        # Error handling
        content.append("\n### Error Handling")
        content.append("- Standardized error response format")
        content.append("- Appropriate HTTP status codes")
        content.append("- User-friendly error messages")
        content.append("- Detailed logging for debugging")

        return "\n".join(content)

    def _generate_integration_requirements(self, inference_result: InferenceResult) -> str:
        """Generate integration requirements section."""

        content = ["## Integration Requirements"]

        # External integrations
        if inference_result.integrations:
            content.append("\n### External Service Integrations")
            for integration in inference_result.integrations:
                content.append(f"- **{integration.title()}**: External service integration")
                content.append(f"  - Authentication and API key management")
                content.append(f"  - Error handling and retry logic")
                content.append(f"  - Rate limiting compliance")

        # Database integration
        if inference_result.key_entities:
            content.append("\n### Database Integration")
            content.append("- Data persistence and retrieval")
            content.append("- Transaction management")
            content.append("- Connection pooling")
            content.append("- Migration support")

        # Internal component integration
        content.append("\n### Internal Component Integration")
        content.append("- Service layer communication")
        content.append("- Event handling and messaging")
        content.append("- Configuration management")
        content.append("- Logging and monitoring")

        return "\n".join(content)

    def _generate_data_requirements(self, inference_result: InferenceResult) -> str:
        """Generate data requirements section."""

        content = ["## Data Requirements"]

        # Data entities
        if inference_result.key_entities:
            content.append("\n### Core Data Entities")
            for entity in inference_result.key_entities:
                content.append(f"- **{entity}**: Core business entity")
                content.append(f"  - Data validation and integrity")
                content.append(f"  - Audit trail and versioning")
                content.append(f"  - Relationship management")

        # Data flow
        content.append("\n### Data Flow Patterns")
        content.append("- Input validation and sanitization")
        content.append("- Data transformation and processing")
        content.append("- Storage and retrieval operations")
        content.append("- Data export and backup procedures")

        # Data quality
        content.append("\n### Data Quality Requirements")
        content.append("- Consistency across all operations")
        content.append("- Completeness of required fields")
        content.append("- Accuracy through validation rules")
        content.append("- Timeliness of data updates")

        return "\n".join(content)

    def _generate_compliance_constraints(self, inference_result: InferenceResult) -> str:
        """Generate compliance and constraints section."""

        content = ["## Compliance and Constraints"]

        # Technical constraints
        content.append("\n### Technical Constraints")
        content.append(f"- Architecture: {inference_result.architecture.value.title()} pattern")
        content.append(f"- Project Type: {inference_result.project_type.value.replace('_', ' ').title()}")

        if inference_result.deployment_targets:
            content.append(f"- Deployment: {', '.join(inference_result.deployment_targets)}")

        # Security constraints
        if inference_result.security_considerations:
            content.append("\n### Security Constraints")
            for consideration in inference_result.security_considerations:
                content.append(f"- {consideration}")

        # Performance constraints
        if inference_result.performance_requirements:
            content.append("\n### Performance Constraints")
            for requirement in inference_result.performance_requirements:
                content.append(f"- {requirement}")

        # Operational constraints
        content.append("\n### Operational Constraints")
        content.append("- System monitoring and alerting")
        content.append("- Backup and disaster recovery")
        content.append("- Maintenance and update procedures")
        content.append("- Documentation and knowledge transfer")

        return "\n".join(content)

    def _generate_success_metrics(self, inference_result: InferenceResult) -> str:
        """Generate success metrics section."""

        content = ["## Success Metrics"]

        # Functional metrics
        content.append("\n### Functional Success Metrics")
        content.append("- All functional requirements implemented and tested")
        content.append("- User acceptance criteria met for all user stories")
        content.append("- API endpoints respond correctly to valid requests")
        content.append("- Data integrity maintained across all operations")

        # Performance metrics
        content.append("\n### Performance Success Metrics")
        if inference_result.performance_requirements:
            for requirement in inference_result.performance_requirements:
                content.append(f"- {requirement}")
        else:
            content.append("- Response times within acceptable limits")
            content.append("- System handles expected load without degradation")
            content.append("- Resource utilization remains within bounds")

        # Quality metrics
        content.append("\n### Quality Success Metrics")
        content.append("- Code coverage above 80% for critical components")
        content.append("- All security vulnerabilities addressed")
        content.append("- Documentation complete and up-to-date")
        content.append("- User feedback indicates satisfaction with functionality")

        return "\n".join(content)

    def _generate_assumptions_dependencies(self, inference_result: InferenceResult) -> str:
        """Generate assumptions and dependencies section."""

        content = ["## Assumptions and Dependencies"]

        # Technical assumptions
        content.append("\n### Technical Assumptions")
        if inference_result.technical_decisions:
            for decision in inference_result.technical_decisions[:3]:
                content.append(f"- {decision.title}: {decision.rationale}")
        else:
            content.append("- Development environment is properly configured")
            content.append("- Required tools and frameworks are available")
            content.append("- Team has necessary technical expertise")

        # External dependencies
        content.append("\n### External Dependencies")
        if inference_result.integrations:
            for integration in inference_result.integrations:
                content.append(f"- {integration.title()} service availability and API stability")
        else:
            content.append("- Third-party services maintain uptime and compatibility")
            content.append("- External APIs remain stable and accessible")

        # Infrastructure dependencies
        content.append("\n### Infrastructure Dependencies")
        if inference_result.deployment_targets:
            for target in inference_result.deployment_targets:
                content.append(f"- {target.title()} deployment platform availability")
        else:
            content.append("- Hosting infrastructure meets performance requirements")
            content.append("- Network connectivity and bandwidth are adequate")
            content.append("- Security measures are properly configured")

        # Operational assumptions
        content.append("\n### Operational Assumptions")
        content.append("- System administrators have necessary access and permissions")
        content.append("- Monitoring and logging infrastructure is in place")
        content.append("- Backup and recovery procedures are established")
        content.append("- Support processes are defined and documented")

        return "\n".join(content)

    def _extract_core_function(self, inference_result: InferenceResult) -> str:
        """Extract the core function from inference results."""
        if inference_result.functional_requirements:
            # Use the first functional requirement as core function
            return inference_result.functional_requirements[0].description
        return "Provide core application functionality"

    def _extract_key_benefits(self, inference_result: InferenceResult) -> str:
        """Extract key benefits from inference results."""
        benefits = []

        if inference_result.project_type.value == 'rest_api':
            benefits.append("Programmatic access to functionality")
        elif inference_result.project_type.value == 'web_application':
            benefits.append("User-friendly web interface")
        elif inference_result.project_type.value == 'cli_tool':
            benefits.append("Automated command-line operations")

        if inference_result.security_considerations:
            benefits.append("Secure data handling")

        if inference_result.performance_requirements:
            benefits.append("High-performance operations")

        return ", ".join(benefits) if benefits else "Efficient and reliable functionality"

    def _infer_endpoint_path(self, user_story: UserStory) -> str:
        """Infer API endpoint path from user story."""
        if user_story.related_models:
            model = user_story.related_models[0].lower()
            if 'create' in user_story.title.lower():
                return f"/api/{model}s"
            elif 'view' in user_story.title.lower() or 'get' in user_story.title.lower():
                return f"/api/{model}s/{{id}}"
            elif 'update' in user_story.title.lower():
                return f"/api/{model}s/{{id}}"
            elif 'delete' in user_story.title.lower():
                return f"/api/{model}s/{{id}}"

        return "/api/resource"

    def _infer_cli_command(self, functional_requirement: FunctionalRequirement) -> str:
        """Infer CLI command from functional requirement."""
        title_lower = functional_requirement.title.lower()

        if 'create' in title_lower:
            resource = functional_requirement.data_models[0].lower() if functional_requirement.data_models else 'item'
            return f"app create {resource} [options]"
        elif 'retrieve' in title_lower or 'get' in title_lower:
            resource = functional_requirement.data_models[0].lower() if functional_requirement.data_models else 'item'
            return f"app get {resource} [id]"
        elif 'update' in title_lower:
            resource = functional_requirement.data_models[0].lower() if functional_requirement.data_models else 'item'
            return f"app update {resource} [id] [options]"
        elif 'delete' in title_lower:
            resource = functional_requirement.data_models[0].lower() if functional_requirement.data_models else 'item'
            return f"app delete {resource} [id]"
        else:
            return f"app {title_lower.replace(' ', '-')} [options]"