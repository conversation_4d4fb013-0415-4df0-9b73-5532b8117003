"""Generator for tasks.md files from code analysis results."""

from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

from ..inference import InferenceResult, FunctionalRequirement


class TasksGenerator:
    """Generates task breakdown documents from inference results."""

    def __init__(self):
        self.task_counter = 1

    def generate_tasks_md(self, inference_result: InferenceResult, project_name: str) -> str:
        """Generate complete tasks.md content from inference results."""

        sections = []

        # Header
        sections.append(self._generate_header(project_name, inference_result))

        # Summary
        sections.append(self._generate_summary(inference_result))

        # Setup Phase
        sections.append(self._generate_setup_phase(inference_result))

        # Test Implementation Phase
        sections.append(self._generate_test_phase(inference_result))

        # Core Implementation Phase
        sections.append(self._generate_core_phase(inference_result))

        # Integration Phase
        sections.append(self._generate_integration_phase(inference_result))

        # Polish Phase
        sections.append(self._generate_polish_phase(inference_result))

        # Parallel Execution Guide
        sections.append(self._generate_parallel_execution_guide(inference_result))

        # Task Dependencies
        sections.append(self._generate_task_dependencies(inference_result))

        return "\n\n".join(sections)

    def _generate_header(self, project_name: str, inference_result: InferenceResult) -> str:
        """Generate document header."""

        return f"""---
description: \"Executable task breakdown for {project_name} implementation\"
scripts:
  sh: scripts/bash/run-task.sh
  ps: scripts/powershell/run-task.ps1
---

# Tasks: {project_name}

## Implementation Overview

**Project Type**: {inference_result.project_type.value.replace('_', ' ').title()}
**Architecture**: {inference_result.architecture.value.title()}
**Approach**: Test-Driven Development with dependency-ordered execution

This document provides {self._estimate_total_tasks(inference_result)} executable tasks organized into 5 phases, with parallel execution markers [P] for independent tasks."""

    def _generate_summary(self, inference_result: InferenceResult) -> str:
        """Generate summary section."""

        total_tasks = self._estimate_total_tasks(inference_result)
        parallel_tasks = self._estimate_parallel_tasks(inference_result)

        return f"""## Task Summary

**Total Tasks**: {total_tasks}
**Parallel Tasks**: ~{parallel_tasks} (marked with [P])
**Execution Order**: Setup → Tests → Core → Integration → Polish

**Key Implementation Areas**:
- {len(inference_result.key_entities)} core entities
- {len(inference_result.functional_requirements)} functional requirements
- {len(inference_result.non_functional_requirements)} non-functional requirements
- {len(inference_result.integrations)} external integrations"""

    def _generate_setup_phase(self, inference_result: InferenceResult) -> str:
        """Generate setup phase tasks."""

        content = ["## Phase 1: Project Setup"]

        # Basic setup tasks
        content.append(f"\n### T{self._next_task()}: Initialize Project Structure")
        content.append("**Description**: Set up the basic project structure and configuration")
        content.append("**Files**: Project root directories and configuration files")
        content.append("**Acceptance**: All directories created, basic config files in place")

        content.append(f"\n### T{self._next_task()}: Configure Development Dependencies [P]")
        content.append("**Description**: Install and configure development tools and dependencies")
        content.append("**Files**: package.json, requirements.txt, or equivalent")
        content.append("**Acceptance**: All dependencies installed, development environment ready")

        # Framework-specific setup
        if inference_result.technical_decisions:
            for decision in inference_result.technical_decisions[:2]:
                if 'framework' in decision.title.lower():
                    content.append(f"\n### T{self._next_task()}: Configure {decision.title.split(':')[-1].strip()} [P]")
                    content.append(f"**Description**: Set up {decision.title.split(':')[-1].strip()} framework configuration")
                    content.append("**Files**: Framework-specific configuration files")
                    content.append("**Acceptance**: Framework configured and ready for development")

        # Database setup (if applicable)
        if any('database' in decision.title.lower() for decision in inference_result.technical_decisions):
            content.append(f"\n### T{self._next_task()}: Setup Database Configuration")
            content.append("**Description**: Configure database connection and initial schema")
            content.append("**Files**: Database configuration and migration files")
            content.append("**Acceptance**: Database connected, schema initialized")

        # Linting and formatting
        content.append(f"\n### T{self._next_task()}: Configure Code Quality Tools [P]")
        content.append("**Description**: Set up linting, formatting, and code quality checks")
        content.append("**Files**: .eslintrc, .prettierrc, or equivalent configuration")
        content.append("**Acceptance**: Code quality tools configured and passing")

        return "\n".join(content)

    def _generate_test_phase(self, inference_result: InferenceResult) -> str:
        """Generate test implementation phase tasks."""

        content = ["## Phase 2: Test Implementation (TDD)"]

        # Contract tests for each functional requirement
        for fr in inference_result.functional_requirements:
            content.append(f"\n### T{self._next_task()}: Contract Tests for {fr.title} [P]")
            content.append(f"**Description**: Implement contract tests for {fr.title.lower()}")

            if fr.api_endpoints:
                content.append(f"**Files**: tests/contract/test_{self._get_test_filename(fr.title)}.py")
                content.append("**Tests**: API endpoint validation, request/response schemas")
            else:
                content.append(f"**Files**: tests/contract/test_{self._get_test_filename(fr.title)}.py")
                content.append("**Tests**: Interface contract validation")

            content.append("**Acceptance**: All contract tests written and failing (no implementation yet)")

        # Integration tests for user stories
        for i, story in enumerate(inference_result.user_stories[:5], 1):
            content.append(f"\n### T{self._next_task()}: Integration Test for {story.title} [P]")
            content.append(f"**Description**: Implement integration test for user story: {story.description[:80]}...")
            content.append(f"**Files**: tests/integration/test_user_story_{i}.py")
            content.append("**Tests**: End-to-end user scenario validation")
            content.append("**Acceptance**: Integration test written and failing")

        # Unit test scaffolding
        content.append(f"\n### T{self._next_task()}: Unit Test Infrastructure [P]")
        content.append("**Description**: Set up unit test infrastructure and utilities")
        content.append("**Files**: tests/unit/__init__.py, test utilities")
        content.append("**Acceptance**: Unit test framework configured and ready")

        return "\n".join(content)

    def _generate_core_phase(self, inference_result: InferenceResult) -> str:
        """Generate core implementation phase tasks."""

        content = ["## Phase 3: Core Implementation"]

        # Data models (can be parallel since they're in different files)
        for entity in inference_result.key_entities:
            content.append(f"\n### T{self._next_task()}: Implement {entity} Model [P]")
            content.append(f"**Description**: Create {entity} data model with fields and validation")
            content.append(f"**Files**: src/models/{entity.lower()}.py")
            content.append("**Dependencies**: Database configuration (T4)")
            content.append("**Acceptance**: Model defined, validation rules implemented, tests passing")

        # Service layer implementation
        for fr in inference_result.functional_requirements:
            content.append(f"\n### T{self._next_task()}: Implement {fr.title} Service")
            content.append(f"**Description**: Business logic implementation for {fr.title.lower()}")
            content.append(f"**Files**: src/services/{self._get_service_filename(fr.title)}.py")

            # Dependencies on models
            if fr.data_models:
                model_deps = [f"T{self._get_model_task_number(model, inference_result)}" for model in fr.data_models[:2]]
                content.append(f"**Dependencies**: {', '.join(model_deps)}")

            content.append("**Acceptance**: Service logic implemented, unit tests passing")

        # API/Interface layer
        if inference_result.project_type.value in ["rest_api", "web_application", "microservice"]:
            for fr in inference_result.functional_requirements:
                content.append(f"\n### T{self._next_task()}: Implement {fr.title} API Endpoint")
                content.append(f"**Description**: API endpoint for {fr.title.lower()}")
                content.append(f"**Files**: src/api/{self._get_api_filename(fr.title)}.py")
                content.append(f"**Dependencies**: Service T{self._get_service_task_number(fr, inference_result)}")
                content.append("**Acceptance**: Endpoint implemented, contract tests passing")

        elif inference_result.project_type.value == "cli_tool":
            for fr in inference_result.functional_requirements:
                content.append(f"\n### T{self._next_task()}: Implement {fr.title} CLI Command")
                content.append(f"**Description**: Command-line interface for {fr.title.lower()}")
                content.append(f"**Files**: src/cli/{self._get_cli_filename(fr.title)}.py")
                content.append(f"**Dependencies**: Service T{self._get_service_task_number(fr, inference_result)}")
                content.append("**Acceptance**: Command implemented, CLI tests passing")

        return "\n".join(content)

    def _generate_integration_phase(self, inference_result: InferenceResult) -> str:
        """Generate integration phase tasks."""

        content = ["## Phase 4: Integration"]

        # External service integrations
        for integration in inference_result.integrations:
            content.append(f"\n### T{self._next_task()}: Integrate {integration.title()} Service")
            content.append(f"**Description**: Implement integration with {integration.title()} external service")
            content.append(f"**Files**: src/integrations/{integration.lower()}_client.py")
            content.append("**Dependencies**: Core services (Phase 3)")
            content.append("**Acceptance**: Integration working, error handling implemented")

        # Database migrations and setup
        if inference_result.key_entities:
            content.append(f"\n### T{self._next_task()}: Database Migrations")
            content.append("**Description**: Create and apply database migrations for all entities")
            content.append("**Files**: migrations/ directory")
            content.append("**Dependencies**: All model implementations")
            content.append("**Acceptance**: Database schema deployed, data integrity verified")

        # Configuration management
        content.append(f"\n### T{self._next_task()}: Configuration Management")
        content.append("**Description**: Implement environment-specific configuration")
        content.append("**Files**: src/config.py, environment files")
        content.append("**Acceptance**: Configuration system working across environments")

        # Logging and monitoring
        content.append(f"\n### T{self._next_task()}: Logging and Monitoring")
        content.append("**Description**: Implement structured logging and basic monitoring")
        content.append("**Files**: src/logging.py, monitoring configuration")
        content.append("**Acceptance**: Logging functional, monitoring endpoints active")

        # Error handling
        content.append(f"\n### T{self._next_task()}: Global Error Handling")
        content.append("**Description**: Implement global error handling and recovery")
        content.append("**Files**: src/errors.py, middleware files")
        content.append("**Acceptance**: Error handling working, user-friendly error messages")

        return "\n".join(content)

    def _generate_polish_phase(self, inference_result: InferenceResult) -> str:
        """Generate polish phase tasks."""

        content = ["## Phase 5: Polish & Optimization"]

        # Security implementation
        if inference_result.security_considerations:
            content.append(f"\n### T{self._next_task()}: Security Implementation [P]")
            content.append("**Description**: Implement security measures and authentication")
            content.append("**Files**: src/auth.py, security middleware")
            content.append("**Acceptance**: Security measures active, authentication working")

        # Performance optimization
        if inference_result.performance_requirements:
            content.append(f"\n### T{self._next_task()}: Performance Optimization [P]")
            content.append("**Description**: Implement caching, query optimization, and performance monitoring")
            content.append("**Files**: Performance-related optimizations across codebase")
            content.append("**Acceptance**: Performance requirements met, monitoring in place")

        # Documentation
        content.append(f"\n### T{self._next_task()}: API Documentation [P]")
        if inference_result.project_type.value in ["rest_api", "web_application"]:
            content.append("**Description**: Generate and publish API documentation")
            content.append("**Files**: docs/api.md, OpenAPI specs")
        elif inference_result.project_type.value == "cli_tool":
            content.append("**Description**: Generate CLI help documentation")
            content.append("**Files**: docs/cli.md, help text")
        else:
            content.append("**Description**: Generate user documentation")
            content.append("**Files**: docs/user-guide.md")

        content.append("**Acceptance**: Documentation complete and accessible")

        # Final integration tests
        content.append(f"\n### T{self._next_task()}: End-to-End Testing [P]")
        content.append("**Description**: Run complete end-to-end test suite")
        content.append("**Files**: tests/e2e/ directory")
        content.append("**Dependencies**: All implementation tasks")
        content.append("**Acceptance**: All tests passing, system fully functional")

        # Deployment preparation
        content.append(f"\n### T{self._next_task()}: Deployment Preparation")
        content.append("**Description**: Prepare for deployment with Docker, CI/CD, and environment setup")
        content.append("**Files**: Dockerfile, deployment scripts")
        content.append("**Dependencies**: All implementation and testing")
        content.append("**Acceptance**: Ready for deployment, CI/CD pipeline functional")

        return "\n".join(content)

    def _generate_parallel_execution_guide(self, inference_result: InferenceResult) -> str:
        """Generate parallel execution guide."""

        content = ["## Parallel Execution Guide"]

        # Phase 1 parallel tasks
        content.append("\n### Phase 1 Parallel Groups")
        content.append("```bash")
        content.append("# Group 1: Independent setup tasks")
        content.append("Task T002 & Task T003 & Task T005  # Dependencies and tools")
        content.append("```")

        # Phase 2 parallel tasks
        content.append("\n### Phase 2 Parallel Groups")
        content.append("```bash")
        content.append("# Group 1: Contract tests (independent files)")

        contract_tasks = []
        task_num = 6  # Assuming tasks start around T006 for Phase 2
        for fr in inference_result.functional_requirements[:3]:  # Show first 3
            contract_tasks.append(f"Task T{task_num:03d}")
            task_num += 1

        content.append(f"{' & '.join(contract_tasks)}")

        content.append("\n# Group 2: Integration tests (independent files)")
        integration_tasks = []
        for i in range(min(3, len(inference_result.user_stories))):
            integration_tasks.append(f"Task T{task_num:03d}")
            task_num += 1
        content.append(f"{' & '.join(integration_tasks)}")
        content.append("```")

        # Phase 3 parallel tasks
        content.append("\n### Phase 3 Parallel Groups")
        content.append("```bash")
        content.append("# Group 1: Model implementations (independent files)")
        model_tasks = []
        for entity in inference_result.key_entities[:3]:
            model_tasks.append(f"Task T{task_num:03d}")
            task_num += 1
        content.append(f"{' & '.join(model_tasks)}")
        content.append("```")

        # Phase 5 parallel tasks
        content.append("\n### Phase 5 Parallel Groups")
        content.append("```bash")
        content.append("# Group 1: Independent polish tasks")
        final_tasks = []
        if inference_result.security_considerations:
            final_tasks.append("Security Task")
        if inference_result.performance_requirements:
            final_tasks.append("Performance Task")
        final_tasks.append("Documentation Task")
        final_tasks.append("E2E Testing Task")

        content.append(" & ".join(final_tasks))
        content.append("```")

        return "\n".join(content)

    def _generate_task_dependencies(self, inference_result: InferenceResult) -> str:
        """Generate task dependencies section."""

        content = ["## Task Dependencies"]

        content.append("\n### Dependency Chain")
        content.append("1. **Setup** → All subsequent tasks")
        content.append("2. **Models** → Services → API/CLI endpoints")
        content.append("3. **Contract Tests** → Implementation tasks")
        content.append("4. **Core Implementation** → Integration tasks")
        content.append("5. **Integration** → Polish tasks")

        content.append("\n### Critical Path")
        content.append("- Project Setup (T001)")
        content.append("- Database Configuration (T004)")

        # Add critical model
        if inference_result.key_entities:
            content.append(f"- {inference_result.key_entities[0]} Model (estimated T006)")

        # Add critical service
        if inference_result.functional_requirements:
            content.append(f"- {inference_result.functional_requirements[0].title} Service")

        content.append("- Integration Configuration")
        content.append("- End-to-End Testing")

        content.append("\n### Parallel Opportunities")
        content.append(f"- **Model Implementation**: {len(inference_result.key_entities)} models can be built in parallel")
        content.append(f"- **Contract Testing**: {len(inference_result.functional_requirements)} test suites can be written in parallel")
        content.append("- **Polish Tasks**: Security, performance, and documentation can be done in parallel")

        return "\n".join(content)

    def _estimate_total_tasks(self, inference_result: InferenceResult) -> int:
        """Estimate total number of tasks."""
        base_setup = 5
        test_tasks = len(inference_result.functional_requirements) + min(5, len(inference_result.user_stories)) + 1
        core_tasks = len(inference_result.key_entities) + len(inference_result.functional_requirements) * 2
        integration_tasks = len(inference_result.integrations) + 4
        polish_tasks = 4

        return base_setup + test_tasks + core_tasks + integration_tasks + polish_tasks

    def _estimate_parallel_tasks(self, inference_result: InferenceResult) -> int:
        """Estimate number of tasks that can run in parallel."""
        return len(inference_result.key_entities) + len(inference_result.functional_requirements) + 3

    def _next_task(self) -> str:
        """Get next task number."""
        task_num = f"{self.task_counter:03d}"
        self.task_counter += 1
        return task_num

    def _get_test_filename(self, title: str) -> str:
        """Convert title to test filename."""
        return title.lower().replace(' ', '_').replace('-', '_')

    def _get_service_filename(self, title: str) -> str:
        """Convert title to service filename."""
        return title.lower().replace(' ', '_').replace('-', '_') + "_service"

    def _get_api_filename(self, title: str) -> str:
        """Convert title to API filename."""
        return title.lower().replace(' ', '_').replace('-', '_') + "_api"

    def _get_cli_filename(self, title: str) -> str:
        """Convert title to CLI filename."""
        return title.lower().replace(' ', '_').replace('-', '_') + "_cmd"

    def _get_model_task_number(self, model: str, inference_result: InferenceResult) -> int:
        """Get estimated task number for a model."""
        # This is a simplified estimation - in a real implementation,
        # you'd track actual task numbers
        try:
            index = inference_result.key_entities.index(model)
            return 10 + index  # Rough estimate based on phase structure
        except ValueError:
            return 10

    def _get_service_task_number(self, fr: FunctionalRequirement, inference_result: InferenceResult) -> int:
        """Get estimated task number for a service."""
        # This is a simplified estimation
        try:
            index = inference_result.functional_requirements.index(fr)
            return 15 + len(inference_result.key_entities) + index
        except ValueError:
            return 15