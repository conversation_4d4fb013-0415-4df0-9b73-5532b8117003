"""Base parser class for language-specific code analysis."""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class Function:
    """Represents a function or method."""
    name: str
    args: List[str]
    return_type: Optional[str]
    docstring: Optional[str]
    decorators: List[str]
    is_async: bool
    line_number: int
    is_public: bool


@dataclass
class Class:
    """Represents a class."""
    name: str
    base_classes: List[str]
    methods: List[Function]
    attributes: List[str]
    docstring: Optional[str]
    decorators: List[str]
    line_number: int
    is_public: bool


@dataclass
class Import:
    """Represents an import statement."""
    module: str
    names: List[str]
    alias: Optional[str]
    is_from_import: bool


@dataclass
class Variable:
    """Represents a variable or constant."""
    name: str
    type_hint: Optional[str]
    value: Optional[str]
    line_number: int
    is_constant: bool


@dataclass
class ParsedFile:
    """Results of parsing a single file."""
    path: Path
    imports: List[Import]
    classes: List[Class]
    functions: List[Function]
    variables: List[Variable]
    comments: List[str]
    docstring: Optional[str]
    syntax_errors: List[str]


class BaseParser(ABC):
    """Base class for language-specific parsers."""

    @abstractmethod
    def parse_file(self, file_path: Path) -> Optional[ParsedFile]:
        """Parse a single file and return structured information."""
        pass

    @abstractmethod
    def extract_api_endpoints(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract API endpoints from parsed file."""
        pass

    @abstractmethod
    def extract_data_models(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract data models/entities from parsed file."""
        pass

    @abstractmethod
    def extract_test_cases(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract test cases from parsed file."""
        pass

    def _is_public_name(self, name: str) -> bool:
        """Check if a name is public (doesn't start with underscore)."""
        return not name.startswith('_')

    def _extract_docstring(self, node) -> Optional[str]:
        """Extract docstring from AST node (to be implemented by subclasses)."""
        return None

    def _safe_parse(self, file_path: Path, parser_func) -> Optional[ParsedFile]:
        """Safely parse a file with error handling."""
        try:
            return parser_func(file_path)
        except (SyntaxError, UnicodeDecodeError, PermissionError) as e:
            return ParsedFile(
                path=file_path,
                imports=[],
                classes=[],
                functions=[],
                variables=[],
                comments=[],
                docstring=None,
                syntax_errors=[str(e)]
            )
        except Exception as e:
            return ParsedFile(
                path=file_path,
                imports=[],
                classes=[],
                functions=[],
                variables=[],
                comments=[],
                docstring=None,
                syntax_errors=[f"Unexpected error: {str(e)}"]
            )