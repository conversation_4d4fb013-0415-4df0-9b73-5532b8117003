"""Go-specific code parser."""

import re
from pathlib import Path
from typing import Dict, List, Any, Optional

from .base_parser import (
    BaseParser, ParsedFile, Function, Class, Import, Variable
)


class GoParser(BaseParser):
    """Parser for Go files using regex patterns."""

    def parse_file(self, file_path: Path) -> Optional[ParsedFile]:
        """Parse a Go file and extract structured information."""
        return self._safe_parse(file_path, self._parse_go_file)

    def _parse_go_file(self, file_path: Path) -> ParsedFile:
        """Internal method to parse Go file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        imports = self._extract_imports(content)
        structs = self._extract_structs(content)  # Go structs as "classes"
        functions = self._extract_functions(content)
        variables = self._extract_variables(content)
        comments = self._extract_comments(content)
        docstring = self._extract_package_doc(content)

        return ParsedFile(
            path=file_path,
            imports=imports,
            classes=structs,  # Treating structs as classes
            functions=functions,
            variables=variables,
            comments=comments,
            docstring=docstring,
            syntax_errors=[]
        )

    def _extract_imports(self, content: str) -> List[Import]:
        """Extract import statements from Go content."""
        imports = []

        # Single import pattern
        single_import = r'import\s+"([^"]+)"'
        matches = re.finditer(single_import, content, re.MULTILINE)
        for match in matches:
            module = match.group(1)
            imports.append(Import(
                module=module,
                names=[],
                alias=None,
                is_from_import=False
            ))

        # Block import pattern
        block_pattern = r'import\s*\(\s*(.*?)\s*\)'
        block_matches = re.finditer(block_pattern, content, re.DOTALL)
        for block_match in block_matches:
            block_content = block_match.group(1)

            # Extract individual imports from block
            import_lines = block_content.split('\n')
            for line in import_lines:
                line = line.strip()
                if line and not line.startswith('//'):
                    # Handle aliased imports: alias "package"
                    alias_pattern = r'(\w+)\s+"([^"]+)"'
                    alias_match = re.match(alias_pattern, line)
                    if alias_match:
                        alias = alias_match.group(1)
                        module = alias_match.group(2)
                        imports.append(Import(
                            module=module,
                            names=[],
                            alias=alias,
                            is_from_import=False
                        ))
                    else:
                        # Simple import: "package"
                        simple_pattern = r'"([^"]+)"'
                        simple_match = re.search(simple_pattern, line)
                        if simple_match:
                            module = simple_match.group(1)
                            imports.append(Import(
                                module=module,
                                names=[],
                                alias=None,
                                is_from_import=False
                            ))

        return imports

    def _extract_functions(self, content: str) -> List[Function]:
        """Extract function definitions from Go content."""
        functions = []

        # Function pattern: func name(args) returnType {
        func_pattern = r'func\s+(?:\([^)]*\)\s+)?(\w+)\s*\(([^)]*)\)\s*([^{]*)\s*\{'
        matches = re.finditer(func_pattern, content, re.MULTILINE)

        for match in matches:
            name = match.group(1)
            args_str = match.group(2)
            return_type_str = match.group(3).strip()

            # Parse arguments
            args = self._parse_go_arguments(args_str)

            # Parse return type
            return_type = return_type_str if return_type_str else None

            # Extract docstring (comment block above function)
            docstring = self._extract_function_docstring(content, match.start())

            # Check if it's a method (has receiver)
            full_match = match.group(0)
            is_method = full_match.startswith('func (')

            functions.append(Function(
                name=name,
                args=args,
                return_type=return_type,
                docstring=docstring,
                decorators=[],
                is_async=False,  # Go doesn't have async keyword
                line_number=content[:match.start()].count('\n') + 1,
                is_public=name[0].isupper()  # Go convention: uppercase = public
            ))

        return functions

    def _extract_structs(self, content: str) -> List[Class]:
        """Extract struct definitions from Go content (treating as classes)."""
        structs = []

        # Struct pattern: type Name struct {
        struct_pattern = r'type\s+(\w+)\s+struct\s*\{'
        matches = re.finditer(struct_pattern, content, re.MULTILINE)

        for match in matches:
            name = match.group(1)

            # Find struct body
            struct_start = match.end()
            brace_count = 1
            pos = struct_start

            while pos < len(content) and brace_count > 0:
                if content[pos] == '{':
                    brace_count += 1
                elif content[pos] == '}':
                    brace_count -= 1
                pos += 1

            struct_body = content[struct_start-1:pos] if pos < len(content) else content[struct_start-1:]

            # Extract fields from struct body
            attributes = self._extract_struct_fields(struct_body)

            # Extract methods for this struct
            methods = self._extract_struct_methods(content, name)

            # Extract docstring
            docstring = self._extract_struct_docstring(content, match.start())

            structs.append(Class(
                name=name,
                base_classes=[],  # Go doesn't have inheritance
                methods=methods,
                attributes=attributes,
                docstring=docstring,
                decorators=[],
                line_number=content[:match.start()].count('\n') + 1,
                is_public=name[0].isupper()
            ))

        return structs

    def _extract_struct_fields(self, struct_body: str) -> List[str]:
        """Extract field names and types from struct body."""
        fields = []

        # Field pattern: Name Type `tags`
        field_pattern = r'(\w+)\s+([^`\n]+)(?:`[^`]*`)?'
        matches = re.finditer(field_pattern, struct_body, re.MULTILINE)

        for match in matches:
            field_name = match.group(1)
            field_type = match.group(2).strip()

            # Skip if it looks like a method or embedded type
            if not field_type or field_type.startswith('func'):
                continue

            fields.append(f"{field_name}: {field_type}")

        return fields

    def _extract_struct_methods(self, content: str, struct_name: str) -> List[Function]:
        """Extract methods for a specific struct."""
        methods = []

        # Method pattern: func (receiver Type) methodName(args) returnType {
        method_pattern = rf'func\s+\([^)]*{struct_name}\)\s+(\w+)\s*\(([^)]*)\)\s*([^{{]*)\s*\{{'
        matches = re.finditer(method_pattern, content, re.MULTILINE)

        for match in matches:
            name = match.group(1)
            args_str = match.group(2)
            return_type_str = match.group(3).strip()

            # Parse arguments
            args = self._parse_go_arguments(args_str)

            # Parse return type
            return_type = return_type_str if return_type_str else None

            # Extract docstring
            docstring = self._extract_function_docstring(content, match.start())

            methods.append(Function(
                name=name,
                args=args,
                return_type=return_type,
                docstring=docstring,
                decorators=[],
                is_async=False,
                line_number=content[:match.start()].count('\n') + 1,
                is_public=name[0].isupper()
            ))

        return methods

    def _extract_variables(self, content: str) -> List[Variable]:
        """Extract variable declarations from Go content."""
        variables = []

        # Variable patterns
        patterns = [
            r'var\s+(\w+)\s+([^=\n]+)(?:\s*=\s*([^;\n]+))?',  # var name Type = value
            r'(\w+)\s*:=\s*([^;\n]+)',  # name := value (short declaration)
            r'const\s+(\w+)\s*=\s*([^;\n]+)',  # const name = value
            r'const\s+(\w+)\s+([^=\n]+)\s*=\s*([^;\n]+)',  # const name Type = value
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                if pattern.startswith(r'(\w+)\s*:='):  # Short declaration
                    name = match.group(1)
                    value = match.group(2).strip()
                    type_hint = None
                    is_constant = False
                elif 'const' in pattern:
                    name = match.group(1)
                    if len(match.groups()) == 3:  # const name Type = value
                        type_hint = match.group(2).strip()
                        value = match.group(3).strip()
                    else:  # const name = value
                        type_hint = None
                        value = match.group(2).strip()
                    is_constant = True
                else:  # var declaration
                    name = match.group(1)
                    type_hint = match.group(2).strip()
                    value = match.group(3).strip() if len(match.groups()) > 2 and match.group(3) else None
                    is_constant = False

                variables.append(Variable(
                    name=name,
                    type_hint=type_hint,
                    value=value,
                    line_number=content[:match.start()].count('\n') + 1,
                    is_constant=is_constant
                ))

        return variables

    def _extract_comments(self, content: str) -> List[str]:
        """Extract comments from Go content."""
        comments = []

        # Single line comments
        single_line = re.findall(r"//\s*(.*)", content)
        comments.extend(single_line)

        # Multi-line comments
        multi_line = re.findall(r"/\*\s*(.*?)\s*\*/", content, re.DOTALL)
        for comment in multi_line:
            lines = comment.split('\n')
            comments.extend([line.strip() for line in lines if line.strip()])

        return comments

    def _extract_package_doc(self, content: str) -> Optional[str]:
        """Extract package-level documentation."""
        # Look for comment block before package declaration
        package_pattern = r'package\s+\w+'
        match = re.search(package_pattern, content)
        if not match:
            return None

        before_package = content[:match.start()]
        lines = before_package.split('\n')

        # Look for comment block at the end
        doc_lines = []
        for line in reversed(lines):
            stripped = line.strip()
            if stripped.startswith('//'):
                doc_lines.insert(0, stripped[2:].strip())
            elif stripped.startswith('/*') or stripped.endswith('*/'):
                # Handle multi-line comment
                comment = stripped.replace('/*', '').replace('*/', '').strip()
                if comment:
                    doc_lines.insert(0, comment)
            elif not stripped:
                continue  # Skip empty lines
            else:
                break  # Stop at non-comment line

        return '\n'.join(doc_lines) if doc_lines else None

    def _extract_function_docstring(self, content: str, func_pos: int) -> Optional[str]:
        """Extract documentation comment for a function."""
        before_func = content[:func_pos]
        lines = before_func.split('\n')

        # Look for comment block immediately before function
        doc_lines = []
        for i in range(len(lines) - 1, -1, -1):
            line = lines[i].strip()
            if line.startswith('//'):
                doc_lines.insert(0, line[2:].strip())
            elif not line:
                continue  # Skip empty lines
            else:
                break

        return '\n'.join(doc_lines) if doc_lines else None

    def _extract_struct_docstring(self, content: str, struct_pos: int) -> Optional[str]:
        """Extract documentation comment for a struct."""
        return self._extract_function_docstring(content, struct_pos)

    def _parse_go_arguments(self, args_str: str) -> List[str]:
        """Parse Go function arguments."""
        if not args_str.strip():
            return []

        args = []
        # Go arguments: name type, name type, ...
        arg_parts = args_str.split(',')
        for part in arg_parts:
            part = part.strip()
            if part:
                args.append(part)

        return args

    def extract_api_endpoints(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract API endpoints from Go file (Gin, Echo, Fiber)."""
        endpoints = []

        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Gin framework patterns
            gin_patterns = [
                r'(?:router|engine|r)\.(GET|POST|PUT|DELETE|PATCH)\s*\(\s*"([^"]+)"',
            ]

            # Echo framework patterns
            echo_patterns = [
                r'e\.(GET|POST|PUT|DELETE|PATCH)\s*\(\s*"([^"]+)"',
            ]

            # Fiber framework patterns
            fiber_patterns = [
                r'app\.(Get|Post|Put|Delete|Patch)\s*\(\s*"([^"]+)"',
            ]

            all_patterns = gin_patterns + echo_patterns + fiber_patterns

            for pattern in all_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
                for match in matches:
                    method = match.group(1).upper()
                    path = match.group(2)

                    framework = 'gin'
                    if 'e.' in match.group(0):
                        framework = 'echo'
                    elif 'app.' in match.group(0):
                        framework = 'fiber'

                    endpoints.append({
                        'path': path,
                        'method': method,
                        'function': 'handler',
                        'args': [],
                        'return_type': None,
                        'docstring': None,
                        'framework': framework,
                        'line_number': content[:match.start()].count('\n') + 1
                    })

        except Exception:
            pass

        return endpoints

    def extract_data_models(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract data models from Go file."""
        models = []

        for struct in parsed_file.classes:  # structs are stored as classes
            # Extract field information
            fields = []
            for attr in struct.attributes:
                if ':' in attr:
                    name, type_info = attr.split(':', 1)
                    fields.append({
                        'name': name.strip(),
                        'type': type_info.strip(),
                        'required': True,  # Go doesn't have optional by default
                        'description': None
                    })

            models.append({
                'name': struct.name,
                'fields': fields,
                'docstring': struct.docstring,
                'base_classes': [],
                'framework': 'go',
                'line_number': struct.line_number
            })

        return models

    def extract_test_cases(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract test cases from Go test file."""
        test_cases = []

        for func in parsed_file.functions:
            # Go test functions start with Test
            if func.name.startswith('Test') and 'testing.T' in ' '.join(func.args):
                test_cases.append({
                    'name': func.name,
                    'type': 'function',
                    'description': func.docstring,
                    'line_number': func.line_number,
                    'framework': 'go test'
                })

        return test_cases