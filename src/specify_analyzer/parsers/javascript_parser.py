"""JavaScript-specific code parser."""

import re
from pathlib import Path
from typing import Dict, List, Any, Optional

from .base_parser import (
    BaseParser, ParsedFile, Function, Class, Import, Variable
)


class JavaScriptParser(BaseParser):
    """Parser for JavaScript files using regex patterns."""

    def parse_file(self, file_path: Path) -> Optional[ParsedFile]:
        """Parse a JavaScript file and extract structured information."""
        return self._safe_parse(file_path, self._parse_js_file)

    def _parse_js_file(self, file_path: Path) -> ParsedFile:
        """Internal method to parse JavaScript file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        imports = self._extract_imports(content)
        classes = self._extract_classes(content)
        functions = self._extract_functions(content)
        variables = self._extract_variables(content)
        comments = self._extract_comments(content)
        docstring = self._extract_file_docstring(content)

        return ParsedFile(
            path=file_path,
            imports=imports,
            classes=classes,
            functions=functions,
            variables=variables,
            comments=comments,
            docstring=docstring,
            syntax_errors=[]
        )

    def _extract_imports(self, content: str) -> List[Import]:
        """Extract import statements from JavaScript content."""
        imports = []

        # ES6 import patterns
        import_patterns = [
            r"import\s+(\w+)\s+from\s+['\"]([^'\"]+)['\"]",  # import Name from 'module'
            r"import\s+\{\s*([^}]+)\s*\}\s+from\s+['\"]([^'\"]+)['\"]",  # import { names } from 'module'
            r"import\s+\*\s+as\s+(\w+)\s+from\s+['\"]([^'\"]+)['\"]",  # import * as name from 'module'
            r"const\s+(\w+)\s*=\s*require\(['\"]([^'\"]+)['\"]\)",  # const name = require('module')
            r"const\s+\{\s*([^}]+)\s*\}\s*=\s*require\(['\"]([^'\"]+)['\"]\)"  # const { names } = require('module')
        ]

        for pattern in import_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                if "require" in pattern:
                    # CommonJS require
                    names = [match.group(1).strip()]
                    module = match.group(2)
                    imports.append(Import(
                        module=module,
                        names=names,
                        alias=None,
                        is_from_import=True
                    ))
                else:
                    # ES6 import
                    names_str = match.group(1)
                    module = match.group(2)

                    if '{' in names_str:
                        # Named imports
                        names = [name.strip() for name in names_str.split(',')]
                    else:
                        # Default import
                        names = [names_str.strip()]

                    imports.append(Import(
                        module=module,
                        names=names,
                        alias=None,
                        is_from_import=True
                    ))

        return imports

    def _extract_functions(self, content: str) -> List[Function]:
        """Extract function definitions from JavaScript content."""
        functions = []

        # Function patterns
        patterns = [
            r"function\s+(\w+)\s*\(([^)]*)\)\s*\{",  # function name() {}
            r"const\s+(\w+)\s*=\s*\(([^)]*)\)\s*=>\s*\{",  # const name = () => {}
            r"(\w+)\s*:\s*function\s*\(([^)]*)\)\s*\{",  # name: function() {}
            r"(\w+)\s*\(([^)]*)\)\s*\{",  # name() {} (method syntax)
            r"async\s+function\s+(\w+)\s*\(([^)]*)\)\s*\{",  # async function
            r"const\s+(\w+)\s*=\s*async\s*\(([^)]*)\)\s*=>\s*\{",  # const name = async () => {}
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                name = match.group(1)
                args_str = match.group(2) if len(match.groups()) > 1 else ""
                args = [arg.strip() for arg in args_str.split(',') if arg.strip()]

                # Check if async
                is_async = 'async' in match.group(0)

                # Extract docstring (JSDoc comment above function)
                docstring = self._extract_function_docstring(content, match.start())

                functions.append(Function(
                    name=name,
                    args=args,
                    return_type=None,  # JavaScript doesn't have explicit return types
                    docstring=docstring,
                    decorators=[],
                    is_async=is_async,
                    line_number=content[:match.start()].count('\n') + 1,
                    is_public=self._is_public_name(name)
                ))

        return functions

    def _extract_classes(self, content: str) -> List[Class]:
        """Extract class definitions from JavaScript content."""
        classes = []

        # Class pattern
        class_pattern = r"class\s+(\w+)(?:\s+extends\s+(\w+))?\s*\{"
        matches = re.finditer(class_pattern, content, re.MULTILINE)

        for match in matches:
            name = match.group(1)
            base_class = match.group(2) if match.group(2) else None
            base_classes = [base_class] if base_class else []

            # Find class body and extract methods
            class_start = match.end()
            brace_count = 1
            pos = class_start

            while pos < len(content) and brace_count > 0:
                if content[pos] == '{':
                    brace_count += 1
                elif content[pos] == '}':
                    brace_count -= 1
                pos += 1

            class_body = content[class_start-1:pos] if pos < len(content) else content[class_start-1:]

            # Extract methods from class body
            methods = self._extract_class_methods(class_body)

            # Extract docstring
            docstring = self._extract_class_docstring(content, match.start())

            classes.append(Class(
                name=name,
                base_classes=base_classes,
                methods=methods,
                attributes=[],  # Hard to extract reliably with regex
                docstring=docstring,
                decorators=[],
                line_number=content[:match.start()].count('\n') + 1,
                is_public=self._is_public_name(name)
            ))

        return classes

    def _extract_class_methods(self, class_body: str) -> List[Function]:
        """Extract methods from class body."""
        methods = []

        # Method patterns inside class
        method_patterns = [
            r"(\w+)\s*\(([^)]*)\)\s*\{",  # method() {}
            r"async\s+(\w+)\s*\(([^)]*)\)\s*\{",  # async method() {}
            r"static\s+(\w+)\s*\(([^)]*)\)\s*\{",  # static method() {}
            r"get\s+(\w+)\s*\(\s*\)\s*\{",  # getter
            r"set\s+(\w+)\s*\(([^)]*)\)\s*\{",  # setter
        ]

        for pattern in method_patterns:
            matches = re.finditer(pattern, class_body, re.MULTILINE)
            for match in matches:
                name = match.group(1)
                args_str = match.group(2) if len(match.groups()) > 1 else ""
                args = [arg.strip() for arg in args_str.split(',') if arg.strip()]

                # Check modifiers
                is_async = 'async' in match.group(0)
                decorators = []
                if 'static' in match.group(0):
                    decorators.append('static')
                if 'get ' in match.group(0):
                    decorators.append('getter')
                if 'set ' in match.group(0):
                    decorators.append('setter')

                methods.append(Function(
                    name=name,
                    args=args,
                    return_type=None,
                    docstring=None,
                    decorators=decorators,
                    is_async=is_async,
                    line_number=class_body[:match.start()].count('\n') + 1,
                    is_public=self._is_public_name(name)
                ))

        return methods

    def _extract_variables(self, content: str) -> List[Variable]:
        """Extract variable declarations from JavaScript content."""
        variables = []

        # Variable patterns
        patterns = [
            r"const\s+(\w+)\s*=\s*([^;]+)",  # const name = value
            r"let\s+(\w+)\s*=\s*([^;]+)",    # let name = value
            r"var\s+(\w+)\s*=\s*([^;]+)",    # var name = value
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                name = match.group(1)
                value = match.group(2).strip()

                # Check if it's a constant (const keyword or uppercase name)
                is_constant = match.group(0).strip().startswith('const') or name.isupper()

                variables.append(Variable(
                    name=name,
                    type_hint=None,  # JavaScript doesn't have explicit types
                    value=value,
                    line_number=content[:match.start()].count('\n') + 1,
                    is_constant=is_constant
                ))

        return variables

    def _extract_comments(self, content: str) -> List[str]:
        """Extract comments from JavaScript content."""
        comments = []

        # Single line comments
        single_line = re.findall(r"//\s*(.*)", content)
        comments.extend(single_line)

        # Multi-line comments
        multi_line = re.findall(r"/\*\s*(.*?)\s*\*/", content, re.DOTALL)
        for comment in multi_line:
            # Split multi-line comments into separate lines
            lines = comment.split('\n')
            comments.extend([line.strip().lstrip('*').strip() for line in lines if line.strip()])

        return comments

    def _extract_file_docstring(self, content: str) -> Optional[str]:
        """Extract file-level documentation comment."""
        # Look for JSDoc comment at the beginning of file
        pattern = r"^\s*/\*\*\s*(.*?)\s*\*/"
        match = re.search(pattern, content, re.DOTALL)
        if match:
            return match.group(1).strip()
        return None

    def _extract_function_docstring(self, content: str, func_pos: int) -> Optional[str]:
        """Extract JSDoc comment for a function."""
        # Look backwards for JSDoc comment
        before_func = content[:func_pos]
        lines = before_func.split('\n')

        # Look for /** comment */ pattern in previous lines
        for i in range(len(lines) - 1, max(0, len(lines) - 10), -1):
            line = lines[i].strip()
            if line.endswith('*/'):
                # Found end of JSDoc, look for start
                for j in range(i, max(0, i - 20), -1):
                    if lines[j].strip().startswith('/**'):
                        # Extract JSDoc content
                        doc_lines = lines[j:i+1]
                        doc_content = '\n'.join(doc_lines)
                        # Clean up JSDoc formatting
                        doc_content = re.sub(r'/\*\*\s*', '', doc_content)
                        doc_content = re.sub(r'\s*\*/', '', doc_content)
                        doc_content = re.sub(r'^\s*\*\s*', '', doc_content, flags=re.MULTILINE)
                        return doc_content.strip()
                break

        return None

    def _extract_class_docstring(self, content: str, class_pos: int) -> Optional[str]:
        """Extract JSDoc comment for a class."""
        return self._extract_function_docstring(content, class_pos)

    def extract_api_endpoints(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract API endpoints from JavaScript file (Express, Fastify)."""
        endpoints = []

        # Read file content to look for route patterns
        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Express.js patterns
            express_patterns = [
                r"app\.(get|post|put|delete|patch)\s*\(\s*['\"]([^'\"]+)['\"]",
                r"router\.(get|post|put|delete|patch)\s*\(\s*['\"]([^'\"]+)['\"]",
            ]

            for pattern in express_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    method = match.group(1).upper()
                    path = match.group(2)

                    endpoints.append({
                        'path': path,
                        'method': method,
                        'function': 'anonymous',
                        'args': [],
                        'return_type': None,
                        'docstring': None,
                        'framework': 'express',
                        'line_number': content[:match.start()].count('\n') + 1
                    })

        except Exception:
            pass

        return endpoints

    def extract_data_models(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract data models from JavaScript file."""
        models = []

        # Look for Mongoose schemas, Sequelize models, etc.
        for cls in parsed_file.classes:
            # Check if it's a model class
            if any(keyword in cls.name.lower() for keyword in ['model', 'schema', 'entity']):
                models.append({
                    'name': cls.name,
                    'fields': [],  # Hard to extract without proper AST
                    'docstring': cls.docstring,
                    'base_classes': cls.base_classes,
                    'framework': 'unknown',
                    'line_number': cls.line_number
                })

        return models

    def extract_test_cases(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract test cases from JavaScript test file."""
        test_cases = []

        # Look for test functions
        for func in parsed_file.functions:
            if any(keyword in func.name.lower() for keyword in ['test', 'it', 'describe']):
                framework = 'unknown'
                if any('jest' in imp.module for imp in parsed_file.imports):
                    framework = 'jest'
                elif any('mocha' in imp.module for imp in parsed_file.imports):
                    framework = 'mocha'

                test_cases.append({
                    'name': func.name,
                    'type': 'function',
                    'description': func.docstring,
                    'line_number': func.line_number,
                    'framework': framework
                })

        return test_cases