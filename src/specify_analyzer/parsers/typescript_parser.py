"""TypeScript-specific code parser."""

import re
from pathlib import Path
from typing import Dict, List, Any, Optional

from .javascript_parser import JavaScriptParser
from .base_parser import ParsedFile, Function, Class, Import, Variable


class TypeScriptParser(JavaScriptParser):
    """Parser for TypeScript files extending JavaScript parser."""

    def _parse_js_file(self, file_path: Path) -> ParsedFile:
        """Parse TypeScript file with type annotations."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        imports = self._extract_ts_imports(content)
        classes = self._extract_ts_classes(content)
        functions = self._extract_ts_functions(content)
        variables = self._extract_ts_variables(content)
        comments = self._extract_comments(content)
        docstring = self._extract_file_docstring(content)

        return ParsedFile(
            path=file_path,
            imports=imports,
            classes=classes,
            functions=functions,
            variables=variables,
            comments=comments,
            docstring=docstring,
            syntax_errors=[]
        )

    def _extract_ts_imports(self, content: str) -> List[Import]:
        """Extract TypeScript import statements with type imports."""
        imports = super()._extract_imports(content)

        # TypeScript-specific import patterns
        ts_patterns = [
            r"import\s+type\s+\{\s*([^}]+)\s*\}\s+from\s+['\"]([^'\"]+)['\"]",  # import type { Types } from 'module'
            r"import\s+type\s+(\w+)\s+from\s+['\"]([^'\"]+)['\"]",  # import type Name from 'module'
        ]

        for pattern in ts_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                names_str = match.group(1)
                module = match.group(2)

                if '{' in names_str:
                    names = [name.strip() for name in names_str.split(',')]
                else:
                    names = [names_str.strip()]

                imports.append(Import(
                    module=module,
                    names=names,
                    alias=None,
                    is_from_import=True
                ))

        return imports

    def _extract_ts_functions(self, content: str) -> List[Function]:
        """Extract TypeScript functions with type annotations."""
        functions = []

        # TypeScript function patterns with return types
        patterns = [
            r"function\s+(\w+)\s*\(([^)]*)\)\s*:\s*([^{]+)\s*\{",  # function name(): ReturnType {}
            r"const\s+(\w+)\s*=\s*\(([^)]*)\)\s*:\s*([^=]+)\s*=>\s*\{",  # const name = (): ReturnType => {}
            r"(\w+)\s*\(([^)]*)\)\s*:\s*([^{]+)\s*\{",  # name(): ReturnType {} (method)
            r"async\s+function\s+(\w+)\s*\(([^)]*)\)\s*:\s*([^{]+)\s*\{",  # async function with return type
            r"const\s+(\w+)\s*=\s*async\s*\(([^)]*)\)\s*:\s*([^=]+)\s*=>\s*\{",  # async arrow function
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                name = match.group(1)
                args_str = match.group(2) if len(match.groups()) > 1 else ""
                return_type = match.group(3).strip() if len(match.groups()) > 2 else None

                # Parse arguments with type annotations
                args = self._parse_ts_arguments(args_str)

                # Check if async
                is_async = 'async' in match.group(0)

                # Extract docstring
                docstring = self._extract_function_docstring(content, match.start())

                functions.append(Function(
                    name=name,
                    args=args,
                    return_type=return_type,
                    docstring=docstring,
                    decorators=[],
                    is_async=is_async,
                    line_number=content[:match.start()].count('\n') + 1,
                    is_public=self._is_public_name(name)
                ))

        # Also get functions without explicit return types
        js_functions = super()._extract_functions(content)

        # Merge, avoiding duplicates
        existing_names = {f.name for f in functions}
        for func in js_functions:
            if func.name not in existing_names:
                functions.append(func)

        return functions

    def _extract_ts_classes(self, content: str) -> List[Class]:
        """Extract TypeScript classes with type annotations and interfaces."""
        classes = super()._extract_classes(content)

        # Extract interfaces as well
        interface_pattern = r"interface\s+(\w+)(?:\s+extends\s+([^{]+))?\s*\{"
        matches = re.finditer(interface_pattern, content, re.MULTILINE)

        for match in matches:
            name = match.group(1)
            extends_clause = match.group(2)
            base_classes = []

            if extends_clause:
                base_classes = [ext.strip() for ext in extends_clause.split(',')]

            # Find interface body
            interface_start = match.end()
            brace_count = 1
            pos = interface_start

            while pos < len(content) and brace_count > 0:
                if content[pos] == '{':
                    brace_count += 1
                elif content[pos] == '}':
                    brace_count -= 1
                pos += 1

            interface_body = content[interface_start-1:pos] if pos < len(content) else content[interface_start-1:]

            # Extract interface properties as attributes
            attributes = self._extract_interface_properties(interface_body)

            # Extract docstring
            docstring = self._extract_class_docstring(content, match.start())

            classes.append(Class(
                name=name,
                base_classes=base_classes,
                methods=[],
                attributes=attributes,
                docstring=docstring,
                decorators=[],
                line_number=content[:match.start()].count('\n') + 1,
                is_public=self._is_public_name(name)
            ))

        return classes

    def _extract_ts_variables(self, content: str) -> List[Variable]:
        """Extract TypeScript variables with type annotations."""
        variables = []

        # TypeScript variable patterns with type annotations
        patterns = [
            r"const\s+(\w+)\s*:\s*([^=]+)\s*=\s*([^;]+)",  # const name: Type = value
            r"let\s+(\w+)\s*:\s*([^=]+)\s*=\s*([^;]+)",    # let name: Type = value
            r"var\s+(\w+)\s*:\s*([^=]+)\s*=\s*([^;]+)",    # var name: Type = value
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                name = match.group(1)
                type_hint = match.group(2).strip()
                value = match.group(3).strip()

                is_constant = match.group(0).strip().startswith('const') or name.isupper()

                variables.append(Variable(
                    name=name,
                    type_hint=type_hint,
                    value=value,
                    line_number=content[:match.start()].count('\n') + 1,
                    is_constant=is_constant
                ))

        # Also get variables without type annotations
        js_variables = super()._extract_variables(content)

        # Merge, avoiding duplicates
        existing_names = {v.name for v in variables}
        for var in js_variables:
            if var.name not in existing_names:
                variables.append(var)

        return variables

    def _parse_ts_arguments(self, args_str: str) -> List[str]:
        """Parse TypeScript function arguments with type annotations."""
        if not args_str.strip():
            return []

        args = []
        for arg in args_str.split(','):
            arg = arg.strip()
            if arg:
                args.append(arg)

        return args

    def _extract_interface_properties(self, interface_body: str) -> List[str]:
        """Extract property names from interface body."""
        properties = []

        # Property patterns in interface
        property_patterns = [
            r"(\w+)\s*\?\s*:\s*([^;,}]+)",  # optional: property?: Type
            r"(\w+)\s*:\s*([^;,}]+)",       # required: property: Type
        ]

        for pattern in property_patterns:
            matches = re.finditer(pattern, interface_body, re.MULTILINE)
            for match in matches:
                prop_name = match.group(1)
                prop_type = match.group(2).strip()
                properties.append(f"{prop_name}: {prop_type}")

        return properties

    def extract_api_endpoints(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract API endpoints from TypeScript file."""
        endpoints = super().extract_api_endpoints(parsed_file)

        # Look for additional TypeScript-specific patterns
        try:
            with open(parsed_file.path, 'r', encoding='utf-8') as f:
                content = f.read()

            # NestJS decorator patterns
            nestjs_patterns = [
                r"@(Get|Post|Put|Delete|Patch)\s*\(\s*['\"]([^'\"]*)['\"]?\s*\)",
            ]

            for pattern in nestjs_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    method = match.group(1).upper()
                    path = match.group(2) if match.group(2) else "/"

                    endpoints.append({
                        'path': path,
                        'method': method,
                        'function': 'decorated_method',
                        'args': [],
                        'return_type': None,
                        'docstring': None,
                        'framework': 'nestjs',
                        'line_number': content[:match.start()].count('\n') + 1
                    })

        except Exception:
            pass

        return endpoints

    def extract_data_models(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract data models from TypeScript file."""
        models = super().extract_data_models(parsed_file)

        # TypeScript interfaces and types as models
        for cls in parsed_file.classes:
            if cls.attributes:  # Interfaces have attributes
                fields = []
                for attr in cls.attributes:
                    if ':' in attr:
                        name, type_info = attr.split(':', 1)
                        required = '?' not in name
                        name = name.replace('?', '').strip()

                        fields.append({
                            'name': name,
                            'type': type_info.strip(),
                            'required': required,
                            'description': None
                        })

                models.append({
                    'name': cls.name,
                    'fields': fields,
                    'docstring': cls.docstring,
                    'base_classes': cls.base_classes,
                    'framework': 'typescript',
                    'line_number': cls.line_number
                })

        return models