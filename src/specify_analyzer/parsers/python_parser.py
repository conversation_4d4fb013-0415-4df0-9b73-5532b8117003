"""Python-specific code parser using AST."""

import ast
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

from .base_parser import (
    BaseParser, ParsedFile, Function, Class, Import, Variable
)


class PythonParser(BaseParser):
    """Parser for Python files using the ast module."""

    def parse_file(self, file_path: Path) -> Optional[ParsedFile]:
        """Parse a Python file and extract structured information."""
        return self._safe_parse(file_path, self._parse_python_file)

    def _parse_python_file(self, file_path: Path) -> ParsedFile:
        """Internal method to parse Python file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        tree = ast.parse(content, filename=str(file_path))

        imports = []
        classes = []
        functions = []
        variables = []
        comments = []

        # Extract module-level docstring
        docstring = ast.get_docstring(tree)

        # Walk through AST nodes
        for node in ast.walk(tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                imports.append(self._extract_import(node))
            elif isinstance(node, ast.ClassDef):
                classes.append(self._extract_class(node))
            elif isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                # Only extract top-level functions
                if self._is_top_level_function(node, tree):
                    functions.append(self._extract_function(node))
            elif isinstance(node, ast.Assign):
                vars_extracted = self._extract_variables(node)
                variables.extend(vars_extracted)

        # Extract comments (requires parsing line by line)
        comments = self._extract_comments(content)

        return ParsedFile(
            path=file_path,
            imports=imports,
            classes=classes,
            functions=functions,
            variables=variables,
            comments=comments,
            docstring=docstring,
            syntax_errors=[]
        )

    def _extract_import(self, node: Union[ast.Import, ast.ImportFrom]) -> Import:
        """Extract import information from AST node."""
        if isinstance(node, ast.ImportFrom):
            module = node.module or ""
            names = [alias.name for alias in node.names]
            alias = node.names[0].asname if len(node.names) == 1 and node.names[0].asname else None
            return Import(
                module=module,
                names=names,
                alias=alias,
                is_from_import=True
            )
        else:  # ast.Import
            names = [alias.name for alias in node.names]
            alias = node.names[0].asname if len(node.names) == 1 and node.names[0].asname else None
            return Import(
                module=names[0] if names else "",
                names=names,
                alias=alias,
                is_from_import=False
            )

    def _extract_function(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]) -> Function:
        """Extract function information from AST node."""
        # Extract arguments
        args = []
        for arg in node.args.args:
            arg_str = arg.arg
            if arg.annotation:
                arg_str += f": {ast.unparse(arg.annotation)}"
            args.append(arg_str)

        # Extract return type
        return_type = None
        if node.returns:
            return_type = ast.unparse(node.returns)

        # Extract decorators
        decorators = [ast.unparse(dec) for dec in node.decorator_list]

        # Extract docstring
        docstring = ast.get_docstring(node)

        return Function(
            name=node.name,
            args=args,
            return_type=return_type,
            docstring=docstring,
            decorators=decorators,
            is_async=isinstance(node, ast.AsyncFunctionDef),
            line_number=node.lineno,
            is_public=self._is_public_name(node.name)
        )

    def _extract_class(self, node: ast.ClassDef) -> Class:
        """Extract class information from AST node."""
        # Extract base classes
        base_classes = [ast.unparse(base) for base in node.bases]

        # Extract methods
        methods = []
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                methods.append(self._extract_function(item))

        # Extract class attributes
        attributes = []
        for item in node.body:
            if isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        attributes.append(target.id)

        # Extract decorators
        decorators = [ast.unparse(dec) for dec in node.decorator_list]

        # Extract docstring
        docstring = ast.get_docstring(node)

        return Class(
            name=node.name,
            base_classes=base_classes,
            methods=methods,
            attributes=attributes,
            docstring=docstring,
            decorators=decorators,
            line_number=node.lineno,
            is_public=self._is_public_name(node.name)
        )

    def _extract_variables(self, node: ast.Assign) -> List[Variable]:
        """Extract variable assignments from AST node."""
        variables = []

        for target in node.targets:
            if isinstance(target, ast.Name):
                # Extract type hint
                type_hint = None
                if hasattr(node, 'type_comment') and node.type_comment:
                    type_hint = node.type_comment

                # Extract value
                value = None
                try:
                    value = ast.unparse(node.value)
                except:
                    value = "<complex expression>"

                # Check if it's a constant (uppercase name)
                is_constant = target.id.isupper()

                variables.append(Variable(
                    name=target.id,
                    type_hint=type_hint,
                    value=value,
                    line_number=node.lineno,
                    is_constant=is_constant
                ))

        return variables

    def _extract_comments(self, content: str) -> List[str]:
        """Extract comments from source code."""
        comments = []
        lines = content.split('\n')

        for line in lines:
            stripped = line.strip()
            if stripped.startswith('#'):
                comments.append(stripped[1:].strip())

        return comments

    def _is_top_level_function(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef], tree: ast.AST) -> bool:
        """Check if function is defined at module level."""
        for parent_node in ast.walk(tree):
            if hasattr(parent_node, 'body') and node in parent_node.body:
                return isinstance(parent_node, ast.Module)
        return False

    def extract_api_endpoints(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract API endpoints from Python file (FastAPI, Flask, Django)."""
        endpoints = []

        # Check for FastAPI patterns
        for func in parsed_file.functions:
            for decorator in func.decorators:
                if any(method in decorator.lower() for method in ['get', 'post', 'put', 'delete', 'patch']):
                    # Extract route path from decorator
                    route_path = self._extract_route_path(decorator)
                    method = self._extract_http_method(decorator)

                    endpoints.append({
                        'path': route_path,
                        'method': method,
                        'function': func.name,
                        'args': func.args,
                        'return_type': func.return_type,
                        'docstring': func.docstring,
                        'framework': 'fastapi' if 'app.' in decorator else 'flask',
                        'line_number': func.line_number
                    })

        # Check for class-based API endpoints (Django)
        for cls in parsed_file.classes:
            if any(base in ['APIView', 'ViewSet', 'GenericAPIView'] for base in cls.base_classes):
                for method in cls.methods:
                    if method.name.lower() in ['get', 'post', 'put', 'delete', 'patch']:
                        endpoints.append({
                            'path': f"/{cls.name.lower()}/",
                            'method': method.name.upper(),
                            'function': f"{cls.name}.{method.name}",
                            'args': method.args,
                            'return_type': method.return_type,
                            'docstring': method.docstring,
                            'framework': 'django',
                            'line_number': method.line_number
                        })

        return endpoints

    def extract_data_models(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract data models from Python file."""
        models = []

        for cls in parsed_file.classes:
            # Check for common model base classes
            model_bases = ['Model', 'BaseModel', 'SQLAlchemyBase', 'Document']
            if any(base in cls.base_classes for base in model_bases):
                # Extract fields from class attributes and method annotations
                fields = []

                for attr in cls.attributes:
                    fields.append({
                        'name': attr,
                        'type': 'Unknown',
                        'required': True,
                        'description': None
                    })

                # Look for type hints in __init__ or class annotations
                for method in cls.methods:
                    if method.name == '__init__':
                        for arg in method.args[1:]:  # Skip 'self'
                            if ':' in arg:
                                name, type_hint = arg.split(':', 1)
                                fields.append({
                                    'name': name.strip(),
                                    'type': type_hint.strip(),
                                    'required': '=' not in arg,
                                    'description': None
                                })

                models.append({
                    'name': cls.name,
                    'fields': fields,
                    'docstring': cls.docstring,
                    'base_classes': cls.base_classes,
                    'framework': self._detect_model_framework(cls.base_classes),
                    'line_number': cls.line_number
                })

        return models

    def extract_test_cases(self, parsed_file: ParsedFile) -> List[Dict[str, Any]]:
        """Extract test cases from Python test file."""
        test_cases = []

        # Extract test functions
        for func in parsed_file.functions:
            if func.name.startswith('test_'):
                test_cases.append({
                    'name': func.name,
                    'type': 'function',
                    'description': func.docstring,
                    'line_number': func.line_number,
                    'framework': 'pytest' if any('pytest' in imp.module for imp in parsed_file.imports) else 'unittest'
                })

        # Extract test classes and methods
        for cls in parsed_file.classes:
            if cls.name.startswith('Test') or 'TestCase' in cls.base_classes:
                for method in cls.methods:
                    if method.name.startswith('test_'):
                        test_cases.append({
                            'name': f"{cls.name}.{method.name}",
                            'type': 'method',
                            'description': method.docstring,
                            'line_number': method.line_number,
                            'framework': 'unittest' if 'TestCase' in cls.base_classes else 'pytest'
                        })

        return test_cases

    def _extract_route_path(self, decorator: str) -> str:
        """Extract route path from decorator string."""
        # Simple regex-like extraction
        if '("' in decorator:
            start = decorator.find('("') + 2
            end = decorator.find('"', start)
            if end > start:
                return decorator[start:end]
        elif "('" in decorator:
            start = decorator.find("('") + 2
            end = decorator.find("'", start)
            if end > start:
                return decorator[start:end]
        return "/"

    def _extract_http_method(self, decorator: str) -> str:
        """Extract HTTP method from decorator string."""
        methods = ['get', 'post', 'put', 'delete', 'patch', 'options', 'head']
        decorator_lower = decorator.lower()

        for method in methods:
            if f'.{method}(' in decorator_lower or f'@{method}(' in decorator_lower:
                return method.upper()

        return "GET"  # Default

    def _detect_model_framework(self, base_classes: List[str]) -> str:
        """Detect which framework is used for data models."""
        if any('Model' in base for base in base_classes):
            if any('django' in base.lower() for base in base_classes):
                return 'django'
            else:
                return 'sqlalchemy'
        elif any('BaseModel' in base for base in base_classes):
            return 'pydantic'
        elif any('Document' in base for base in base_classes):
            return 'mongoengine'
        return 'unknown'