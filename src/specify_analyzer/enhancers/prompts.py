"""Prompt engineering for document enhancement."""

from typing import Dict, Any
from .base import DocumentType, EnhancementLevel


class PromptTemplates:
    """Templates for AI enhancement prompts."""

    @staticmethod
    def get_enhancement_prompt(
        document_type: DocumentType,
        level: EnhancementLevel,
        project_context: Dict[str, Any]
    ) -> str:
        """Get enhancement prompt for specific document type and level."""
        base_prompts = {
            DocumentType.SPEC: PromptTemplates._get_spec_prompt,
            DocumentType.PLAN: PromptTemplates._get_plan_prompt,
            DocumentType.DATA_MODEL: PromptTemplates._get_data_model_prompt,
            DocumentType.TASKS: PromptTemplates._get_tasks_prompt,
            DocumentType.RESEARCH: PromptTemplates._get_research_prompt,
            DocumentType.QUICKSTART: PromptTemplates._get_quickstart_prompt,
        }

        prompt_func = base_prompts.get(document_type)
        if not prompt_func:
            return "Please improve this document by enhancing clarity and completeness."

        return prompt_func(level, project_context)

    @staticmethod
    def _get_spec_prompt(level: EnhancementLevel, context: Dict[str, Any]) -> str:
        """Get spec.md enhancement prompt."""
        base_prompt = """Enhance this specification document by:

1. **Functional Requirements**: Expand user stories with detailed acceptance criteria
2. **Non-Functional Requirements**: Add performance, security, and scalability requirements
3. **API Specifications**: Detail request/response formats, error codes, authentication
4. **User Interface**: Describe user flows, wireframes, and interaction patterns
5. **Business Logic**: Clarify complex workflows and business rules"""

        if level == EnhancementLevel.LIGHT:
            return base_prompt + "\n\nFocus on clarifying existing requirements and fixing inconsistencies."

        elif level == EnhancementLevel.MODERATE:
            return base_prompt + """

Additional enhancements:
- Add missing edge cases and error scenarios
- Include accessibility and internationalization requirements
- Specify data validation rules and constraints
- Add integration requirements with external systems"""

        else:  # COMPREHENSIVE
            return base_prompt + """

Comprehensive enhancements:
- Detailed user personas and use case scenarios
- Complete API documentation with examples
- Security requirements (authentication, authorization, data protection)
- Performance benchmarks and scalability targets
- Compliance requirements (GDPR, accessibility standards)
- Risk analysis and mitigation strategies
- Success metrics and KPIs"""

    @staticmethod
    def _get_plan_prompt(level: EnhancementLevel, context: Dict[str, Any]) -> str:
        """Get plan.md enhancement prompt."""
        base_prompt = f"""Enhance this technical implementation plan for a {context.get('project_type', 'software')} project using {context.get('primary_language', 'unknown language')}:

1. **Architecture**: Refine system design and component interactions
2. **Technology Stack**: Justify technology choices and alternatives
3. **Development Phases**: Improve phase breakdown and dependencies
4. **Infrastructure**: Detail deployment and scaling strategies
5. **Risk Management**: Identify technical risks and mitigation plans"""

        if level == EnhancementLevel.LIGHT:
            return base_prompt + "\n\nFocus on clarifying the technical approach and fixing any gaps."

        elif level == EnhancementLevel.MODERATE:
            return base_prompt + """

Additional enhancements:
- Compare alternative architectural patterns
- Add detailed database design and data flow
- Include CI/CD pipeline specifications
- Specify monitoring and logging strategies
- Add security architecture considerations"""

        else:  # COMPREHENSIVE
            return base_prompt + """

Comprehensive enhancements:
- Detailed microservices/component boundaries
- Performance optimization strategies
- Disaster recovery and backup plans
- Cost analysis and resource planning
- Migration strategies and rollback procedures
- Third-party integrations and API designs
- Testing strategy across all levels"""

    @staticmethod
    def _get_data_model_prompt(level: EnhancementLevel, context: Dict[str, Any]) -> str:
        """Get data-model.md enhancement prompt."""
        base_prompt = """Enhance this data model documentation by:

1. **Entity Relationships**: Clarify relationships and cardinalities
2. **Data Validation**: Add validation rules and constraints
3. **Data Flow**: Improve data flow diagrams and transformations
4. **Storage Strategy**: Optimize storage and indexing strategies
5. **Data Lifecycle**: Define creation, update, and deletion policies"""

        if level == EnhancementLevel.LIGHT:
            return base_prompt + "\n\nFocus on completing missing relationships and validation rules."

        elif level == EnhancementLevel.MODERATE:
            return base_prompt + """

Additional enhancements:
- Add data versioning and migration strategies
- Include data privacy and security considerations
- Specify caching and performance optimizations
- Add audit trail and change tracking
- Define data archival and retention policies"""

        else:  # COMPREHENSIVE
            return base_prompt + """

Comprehensive enhancements:
- Complete entity-relationship diagrams
- Data governance and quality standards
- GDPR/privacy compliance considerations
- Multi-tenant data isolation strategies
- Analytics and reporting data models
- Real-time vs batch processing patterns
- Data backup and recovery procedures"""

    @staticmethod
    def _get_tasks_prompt(level: EnhancementLevel, context: Dict[str, Any]) -> str:
        """Get tasks.md enhancement prompt."""
        base_prompt = """Enhance this task breakdown by:

1. **Task Granularity**: Break complex tasks into manageable subtasks
2. **Dependencies**: Clarify task dependencies and critical path
3. **Time Estimates**: Add realistic time estimates for each task
4. **Parallel Execution**: Identify tasks that can run in parallel [P]
5. **Acceptance Criteria**: Define clear completion criteria for each task"""

        if level == EnhancementLevel.LIGHT:
            return base_prompt + "\n\nFocus on improving task clarity and adding missing dependencies."

        elif level == EnhancementLevel.MODERATE:
            return base_prompt + """

Additional enhancements:
- Add skill requirements for each task
- Include testing tasks and quality gates
- Specify tools and resources needed
- Add risk assessment for complex tasks
- Include code review and approval processes"""

        else:  # COMPREHENSIVE
            return base_prompt + """

Comprehensive enhancements:
- Detailed work breakdown structure (WBS)
- Resource allocation and team assignments
- Multiple estimation techniques (optimistic/pessimistic/most likely)
- Risk mitigation tasks and contingency plans
- Integration and deployment tasks
- Documentation and training tasks
- Performance monitoring and optimization tasks"""

    @staticmethod
    def _get_research_prompt(level: EnhancementLevel, context: Dict[str, Any]) -> str:
        """Get research.md enhancement prompt."""
        frameworks = context.get('frameworks', [])
        framework_text = f" (currently using {', '.join(frameworks)})" if frameworks else ""

        base_prompt = f"""Enhance this research document{framework_text} by:

1. **Technology Comparison**: Add detailed pros/cons analysis
2. **Architecture Patterns**: Compare alternative patterns and trade-offs
3. **Performance Analysis**: Include benchmarks and performance considerations
4. **Future Considerations**: Identify emerging technologies and trends
5. **Decision Rationale**: Strengthen justification for technology choices"""

        if level == EnhancementLevel.LIGHT:
            return base_prompt + "\n\nFocus on completing missing comparisons and adding basic rationale."

        elif level == EnhancementLevel.MODERATE:
            return base_prompt + """

Additional enhancements:
- Add learning curve and team expertise considerations
- Include community support and ecosystem analysis
- Evaluate vendor lock-in and migration risks
- Add cost analysis (licensing, infrastructure, development)
- Include security and compliance considerations"""

        else:  # COMPREHENSIVE
            return base_prompt + """

Comprehensive enhancements:
- Market analysis and competitive landscape
- Long-term maintenance and support considerations
- Integration complexity with existing systems
- Scalability and performance benchmarks
- Risk analysis and mitigation strategies
- Alternative solution architectures
- Proof-of-concept recommendations and validation plans"""

    @staticmethod
    def _get_quickstart_prompt(level: EnhancementLevel, context: Dict[str, Any]) -> str:
        """Get quickstart.md enhancement prompt."""
        language = context.get('primary_language', 'the chosen language')

        base_prompt = f"""Enhance this quickstart guide for {language} development by:

1. **Setup Instructions**: Improve development environment setup
2. **Code Examples**: Add practical, working code examples
3. **Common Scenarios**: Include typical use cases and workflows
4. **Troubleshooting**: Expand troubleshooting guide with solutions
5. **Testing**: Add examples for running and writing tests"""

        if level == EnhancementLevel.LIGHT:
            return base_prompt + "\n\nFocus on fixing broken examples and clarifying setup steps."

        elif level == EnhancementLevel.MODERATE:
            return base_prompt + """

Additional enhancements:
- Add multiple IDE/editor configurations
- Include Docker/containerization examples
- Add CI/CD pipeline examples
- Include debugging techniques and tools
- Add performance optimization tips"""

        else:  # COMPREHENSIVE
            return base_prompt + """

Comprehensive enhancements:
- Complete tutorial with step-by-step walkthrough
- Multiple deployment scenarios (local, staging, production)
- Advanced configuration and customization options
- Integration examples with popular tools and services
- Best practices and design patterns
- Performance monitoring and optimization guide
- Security configuration and hardening tips"""