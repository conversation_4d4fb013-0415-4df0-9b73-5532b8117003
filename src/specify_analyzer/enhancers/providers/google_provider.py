"""Google Gemini provider implementation."""

import json
from typing import Dict, List, Any

try:
    import httpx
except ImportError:
    httpx = None

from ..base import BaseAIProvider, EnhancementConfig


class GoogleProvider(BaseAIProvider):
    """Google Gemini provider for document enhancement."""

    def __init__(self, config: EnhancementConfig):
        super().__init__(config)
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"

        if httpx is None:
            raise ImportError("httpx is required for Google provider. Install with: pip install httpx")

    def _requires_api_key(self) -> bool:
        """Google requires an API key."""
        return True

    def _get_api_key_env_var(self) -> str:
        """Environment variable for Google API key."""
        return "GOOGLE_API_KEY"

    def _estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Estimate cost based on Google pricing."""
        # Gemini pricing (approximate, as of 2024)
        pricing = {
            "gemini-pro": {"input": 0.0005 / 1000, "output": 0.0015 / 1000},
            "gemini-pro-vision": {"input": 0.0025 / 1000, "output": 0.0075 / 1000},
        }

        model_pricing = pricing.get(self.config.model, pricing["gemini-pro"])
        return (input_tokens * model_pricing["input"] +
                output_tokens * model_pricing["output"])

    def _make_api_call(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """Make API call to Google Gemini."""
        # Convert messages to Gemini format
        prompt_parts = []

        for msg in messages:
            if msg["role"] == "system":
                prompt_parts.append(f"System: {msg['content']}")
            elif msg["role"] == "user":
                prompt_parts.append(f"User: {msg['content']}")

        combined_prompt = "\n\n".join(prompt_parts)

        payload = {
            "contents": [{
                "parts": [{"text": combined_prompt}]
            }],
            "generationConfig": {
                "maxOutputTokens": self.config.max_tokens,
                "temperature": self.config.temperature
            }
        }

        url = f"{self.base_url}/models/{self.config.model}:generateContent"
        params = {"key": self.config.api_key}

        with httpx.Client(timeout=self.config.timeout) as client:
            response = client.post(url, params=params, json=payload)
            response.raise_for_status()
            return response.json()

    def _extract_content(self, response: Dict[str, Any]) -> str:
        """Extract enhanced content from Google response."""
        candidates = response.get("candidates", [])
        if candidates:
            content = candidates[0].get("content", {})
            parts = content.get("parts", [])
            if parts:
                return parts[0].get("text", "")
        return ""

    def _extract_token_usage(self, response: Dict[str, Any]) -> int:
        """Extract token usage from Google response."""
        usage = response.get("usageMetadata", {})
        return usage.get("candidatesTokenCount", 0)