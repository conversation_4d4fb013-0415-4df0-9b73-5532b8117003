"""Anthropic Claude provider implementation."""

import json
from typing import Dict, List, Any

try:
    import httpx
except ImportError:
    httpx = None

from ..base import BaseAIProvider, EnhancementConfig


class AnthropicProvider(BaseAIProvider):
    """Anthropic Claude provider for document enhancement."""

    def __init__(self, config: EnhancementConfig):
        super().__init__(config)
        self.base_url = "https://api.anthropic.com/v1"

        if httpx is None:
            raise ImportError("httpx is required for Anthropic provider. Install with: pip install httpx")

    def _requires_api_key(self) -> bool:
        """Anthropic requires an API key."""
        return True

    def _get_api_key_env_var(self) -> str:
        """Environment variable for Anthropic API key."""
        return "ANTHROPIC_API_KEY"

    def _estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Estimate cost based on Anthropic pricing."""
        # Claude pricing (approximate, as of 2024)
        pricing = {
            "claude-3-opus-20240229": {"input": 0.015 / 1000, "output": 0.075 / 1000},
            "claude-3-sonnet-20240229": {"input": 0.003 / 1000, "output": 0.015 / 1000},
            "claude-3-haiku-20240307": {"input": 0.00025 / 1000, "output": 0.00125 / 1000},
        }

        model_pricing = pricing.get(self.config.model, pricing["claude-3-sonnet-20240229"])
        return (input_tokens * model_pricing["input"] +
                output_tokens * model_pricing["output"])

    def _make_api_call(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """Make API call to Anthropic."""
        headers = {
            "x-api-key": self.config.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }

        # Convert messages format for Anthropic
        system_message = ""
        user_messages = []

        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                user_messages.append(msg)

        payload = {
            "model": self.config.model,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "messages": user_messages
        }

        if system_message:
            payload["system"] = system_message

        with httpx.Client(timeout=self.config.timeout) as client:
            response = client.post(
                f"{self.base_url}/messages",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()

    def _extract_content(self, response: Dict[str, Any]) -> str:
        """Extract enhanced content from Anthropic response."""
        content = response["content"]
        if isinstance(content, list) and len(content) > 0:
            return content[0]["text"]
        return str(content)

    def _extract_token_usage(self, response: Dict[str, Any]) -> int:
        """Extract token usage from Anthropic response."""
        usage = response.get("usage", {})
        return usage.get("output_tokens", 0)