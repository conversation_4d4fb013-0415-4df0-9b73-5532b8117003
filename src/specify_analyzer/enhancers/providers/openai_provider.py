"""OpenAI GPT provider implementation."""

import json
from typing import Dict, List, Any

try:
    import httpx
except ImportError:
    httpx = None

from ..base import BaseAIProvider, EnhancementConfig


class OpenAIProvider(BaseAIProvider):
    """OpenAI GPT provider for document enhancement."""

    def __init__(self, config: EnhancementConfig):
        super().__init__(config)
        self.base_url = "https://api.openai.com/v1"

        if httpx is None:
            raise ImportError("httpx is required for OpenAI provider. Install with: pip install httpx")

    def _requires_api_key(self) -> bool:
        """OpenAI requires an API key."""
        return True

    def _get_api_key_env_var(self) -> str:
        """Environment variable for OpenAI API key."""
        return "OPENAI_API_KEY"

    def _estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Estimate cost based on OpenAI pricing."""
        # GPT-4 pricing (approximate, as of 2024)
        pricing = {
            "gpt-4": {"input": 0.03 / 1000, "output": 0.06 / 1000},
            "gpt-4-turbo": {"input": 0.01 / 1000, "output": 0.03 / 1000},
            "gpt-3.5-turbo": {"input": 0.001 / 1000, "output": 0.002 / 1000},
        }

        model_pricing = pricing.get(self.config.model, pricing["gpt-4"])
        return (input_tokens * model_pricing["input"] +
                output_tokens * model_pricing["output"])

    def _make_api_call(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """Make API call to OpenAI."""
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.config.model,
            "messages": messages,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature
        }

        with httpx.Client(timeout=self.config.timeout) as client:
            response = client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()

    def _extract_content(self, response: Dict[str, Any]) -> str:
        """Extract enhanced content from OpenAI response."""
        return response["choices"][0]["message"]["content"]

    def _extract_token_usage(self, response: Dict[str, Any]) -> int:
        """Extract token usage from OpenAI response."""
        usage = response.get("usage", {})
        return usage.get("completion_tokens", 0)