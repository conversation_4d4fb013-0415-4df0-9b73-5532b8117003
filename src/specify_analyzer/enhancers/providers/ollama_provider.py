"""Ollama local model provider implementation."""

import json
from typing import Dict, List, Any

try:
    import httpx
except ImportError:
    httpx = None

from ..base import BaseAIProvider, EnhancementConfig


class OllamaProvider(BaseAIProvider):
    """Ollama local model provider for document enhancement."""

    def __init__(self, config: EnhancementConfig):
        super().__init__(config)
        self.base_url = "http://localhost:11434/api"

        if httpx is None:
            raise ImportError("httpx is required for Ollama provider. Install with: pip install httpx")

    def _requires_api_key(self) -> bool:
        """Ollama typically doesn't require an API key for local models."""
        return False

    def _get_api_key_env_var(self) -> str:
        """No API key needed for local Ollama."""
        return ""

    def _estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Local models are free to use."""
        return 0.0

    def _make_api_call(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """Make API call to local Ollama instance."""
        # Convert messages to a single prompt for Ollama
        prompt_parts = []

        for msg in messages:
            if msg["role"] == "system":
                prompt_parts.append(f"System: {msg['content']}")
            elif msg["role"] == "user":
                prompt_parts.append(f"User: {msg['content']}")

        combined_prompt = "\n\n".join(prompt_parts)

        payload = {
            "model": self.config.model,
            "prompt": combined_prompt,
            "stream": False,
            "options": {
                "num_predict": self.config.max_tokens,
                "temperature": self.config.temperature
            }
        }

        with httpx.Client(timeout=self.config.timeout) as client:
            response = client.post(
                f"{self.base_url}/generate",
                json=payload
            )
            response.raise_for_status()
            return response.json()

    def _extract_content(self, response: Dict[str, Any]) -> str:
        """Extract enhanced content from Ollama response."""
        return response.get("response", "")

    def _extract_token_usage(self, response: Dict[str, Any]) -> int:
        """Extract token usage from Ollama response."""
        # Ollama doesn't provide detailed token usage, estimate from response length
        content = response.get("response", "")
        return len(content) // 4  # Rough estimation

    def check_model_availability(self) -> bool:
        """Check if the specified model is available in Ollama."""
        try:
            with httpx.Client(timeout=10) as client:
                response = client.get(f"{self.base_url}/tags")
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    available_models = [model["name"] for model in models]
                    return self.config.model in available_models
        except Exception:
            pass
        return False

    def pull_model_if_needed(self) -> bool:
        """Pull the model if it's not available locally."""
        if self.check_model_availability():
            return True

        try:
            payload = {"name": self.config.model}
            with httpx.Client(timeout=300) as client:  # Model pulling can take time
                response = client.post(f"{self.base_url}/pull", json=payload)
                return response.status_code == 200
        except Exception:
            return False