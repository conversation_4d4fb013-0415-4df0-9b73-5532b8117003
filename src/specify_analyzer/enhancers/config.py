"""Configuration management for AI enhancement."""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

from .base import EnhancementLevel


@dataclass
class EnhancementConfigFile:
    """Configuration file for enhancement settings."""
    default_provider: str = "openai"
    default_model: str = "gpt-4"
    default_level: str = "moderate"
    default_cost_limit: Optional[float] = None
    provider_configs: Dict[str, Dict[str, Any]] = None
    custom_prompts: Dict[str, str] = None
    sections_to_enhance: Dict[str, list] = None

    def __post_init__(self):
        if self.provider_configs is None:
            self.provider_configs = {}
        if self.custom_prompts is None:
            self.custom_prompts = {}
        if self.sections_to_enhance is None:
            self.sections_to_enhance = {}


class ConfigManager:
    """Manages enhancement configuration files."""

    DEFAULT_CONFIG_FILENAME = ".specify-enhance.json"

    @classmethod
    def load_config(cls, project_path: Path) -> EnhancementConfigFile:
        """Load configuration from project directory or user home."""
        # Check project directory first
        project_config = project_path / cls.DEFAULT_CONFIG_FILENAME
        if project_config.exists():
            return cls._load_from_file(project_config)

        # Check user home directory
        home_config = Path.home() / cls.DEFAULT_CONFIG_FILENAME
        if home_config.exists():
            return cls._load_from_file(home_config)

        # Return default configuration
        return EnhancementConfigFile()

    @classmethod
    def _load_from_file(cls, config_path: Path) -> EnhancementConfigFile:
        """Load configuration from a specific file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return EnhancementConfigFile(**config_data)
        except (json.JSONDecodeError, TypeError, FileNotFoundError):
            return EnhancementConfigFile()

    @classmethod
    def save_config(cls, config: EnhancementConfigFile, project_path: Path, global_config: bool = False):
        """Save configuration to file."""
        if global_config:
            config_path = Path.home() / cls.DEFAULT_CONFIG_FILENAME
        else:
            config_path = project_path / cls.DEFAULT_CONFIG_FILENAME

        config_path.parent.mkdir(parents=True, exist_ok=True)

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(asdict(config), f, indent=2)

    @classmethod
    def create_sample_config(cls, project_path: Path):
        """Create a sample configuration file with common settings."""
        sample_config = EnhancementConfigFile(
            default_provider="openai",
            default_model="gpt-4",
            default_level="moderate",
            default_cost_limit=10.0,
            provider_configs={
                "openai": {
                    "models": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
                    "temperature": 0.3,
                    "max_tokens": 4000
                },
                "anthropic": {
                    "models": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
                    "temperature": 0.3,
                    "max_tokens": 4000
                },
                "google": {
                    "models": ["gemini-pro", "gemini-pro-vision"],
                    "temperature": 0.3,
                    "max_tokens": 4000
                },
                "ollama": {
                    "models": ["llama2", "codellama", "mistral"],
                    "temperature": 0.3,
                    "max_tokens": 4000
                }
            },
            custom_prompts={
                "spec_enhancement": "Focus on user experience and business value when enhancing requirements.",
                "plan_enhancement": "Emphasize scalability and maintainability in technical planning.",
                "data_model_enhancement": "Ensure data privacy and security considerations are included.",
                "tasks_enhancement": "Break down complex tasks into smaller, testable units.",
                "research_enhancement": "Include cost-benefit analysis for technology choices.",
                "quickstart_enhancement": "Make examples practical and copy-paste ready."
            },
            sections_to_enhance={
                "spec.md": ["functional_requirements", "user_stories", "api_specifications"],
                "plan.md": ["architecture", "technology_stack", "development_phases"],
                "data-model.md": ["entities", "relationships", "validation"],
                "tasks.md": ["task_breakdown", "dependencies", "estimates"],
                "research.md": ["comparison", "rationale", "future_considerations"],
                "quickstart.md": ["setup", "examples", "troubleshooting"]
            }
        )

        cls.save_config(sample_config, project_path, global_config=False)
        return project_path / cls.DEFAULT_CONFIG_FILENAME

    @classmethod
    def get_api_key_from_config(cls, provider: str, config: EnhancementConfigFile) -> Optional[str]:
        """Get API key from configuration or environment."""
        # Check provider-specific config first
        provider_config = config.provider_configs.get(provider, {})
        api_key = provider_config.get('api_key')

        if api_key:
            return api_key

        # Fall back to environment variables
        env_vars = {
            'openai': 'OPENAI_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'google': 'GOOGLE_API_KEY',
            'ollama': '',  # No API key needed for local Ollama
        }

        env_var = env_vars.get(provider)
        if env_var:
            return os.getenv(env_var)

        return None