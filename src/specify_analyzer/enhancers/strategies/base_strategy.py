"""Base strategy class for document enhancement."""

from abc import ABC, abstractmethod
from typing import Dict, Any, List
from ..base import BaseAIProvider, DocumentType, EnhancementResult
from ..prompts import PromptTemplates


class BaseEnhancementStrategy(ABC):
    """Base class for document enhancement strategies."""

    def __init__(self, document_type: DocumentType):
        self.document_type = document_type

    @abstractmethod
    def get_sections_to_enhance(self, content: str) -> List[str]:
        """Identify which sections of the document need enhancement."""
        pass

    @abstractmethod
    def preprocess_content(self, content: str) -> str:
        """Preprocess content before enhancement."""
        pass

    @abstractmethod
    def postprocess_content(self, enhanced_content: str) -> str:
        """Postprocess enhanced content."""
        pass

    def enhance_document(
        self,
        provider: BaseAIProvider,
        content: str,
        project_context: Dict[str, Any]
    ) -> EnhancementResult:
        """Enhance the document using the AI provider."""

        # Preprocess content
        processed_content = self.preprocess_content(content)

        # Get enhancement prompt
        enhancement_prompt = PromptTemplates.get_enhancement_prompt(
            self.document_type,
            provider.config.level,
            project_context
        )

        # Enhance with AI
        result = provider.enhance_document(
            processed_content,
            self.document_type,
            project_context,
            enhancement_prompt
        )

        # Postprocess if successful
        if result.success and result.enhanced_content:
            result.enhanced_content = self.postprocess_content(result.enhanced_content)
            result.sections_enhanced = self.get_sections_to_enhance(content)

        return result

    def _extract_sections(self, content: str) -> Dict[str, str]:
        """Extract sections from markdown content."""
        sections = {}
        current_section = ""
        current_content = []

        lines = content.split('\n')
        for line in lines:
            if line.strip().startswith('#'):
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content)

                current_section = line.strip()
                current_content = [line]
            else:
                current_content.append(line)

        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content)

        return sections

    def _validate_markdown_structure(self, content: str) -> bool:
        """Validate that the enhanced content maintains proper markdown structure."""
        lines = content.split('\n')
        has_headers = False

        for line in lines:
            if line.strip().startswith('#'):
                has_headers = True
                break

        return has_headers