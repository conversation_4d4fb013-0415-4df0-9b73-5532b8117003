"""QuickstartEnhancer for improving quickstart documentation."""

import re
from typing import List, Dict, Any
from .base_strategy import BaseEnhancementStrategy
from ..base import DocumentType


class QuickstartEnhancer(BaseEnhancementStrategy):
    """Enhancement strategy for quickstart.md documents."""

    def __init__(self):
        super().__init__(DocumentType.QUICKSTART)

    def get_sections_to_enhance(self, content: str) -> List[str]:
        """Identify sections that need enhancement in quickstart documents."""
        sections = []

        if "setup" in content.lower() or "installation" in content.lower():
            sections.append("Setup Instructions")
        if "example" in content.lower() or "code" in content.lower():
            sections.append("Code Examples")
        if "scenario" in content.lower() or "workflow" in content.lower():
            sections.append("Common Scenarios")
        if "troubleshooting" in content.lower() or "issues" in content.lower():
            sections.append("Troubleshooting")
        if "test" in content.lower():
            sections.append("Testing")

        return sections

    def preprocess_content(self, content: str) -> str:
        """Preprocess quickstart content before enhancement."""
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = self._standardize_code_blocks(content)
        return content.strip()

    def postprocess_content(self, enhanced_content: str) -> str:
        """Postprocess enhanced quickstart content."""
        enhanced_content = self._fix_section_hierarchy(enhanced_content)
        enhanced_content = self._format_installation_steps(enhanced_content)
        enhanced_content = self._format_code_examples(enhanced_content)
        enhanced_content = self._format_troubleshooting(enhanced_content)
        return enhanced_content

    def _standardize_code_blocks(self, content: str) -> str:
        """Standardize code block formatting."""
        # Ensure proper code block fencing
        lines = content.split('\n')
        fixed_lines = []
        in_code_block = False

        for line in lines:
            # Detect code blocks
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
            elif not in_code_block and line.strip() and not line.startswith(' ' * 4):
                # Check if this looks like code (contains common code patterns)
                code_indicators = ['()', '{', '}', ';', '=', 'import ', 'from ', 'def ', 'class ', 'function']
                if any(indicator in line for indicator in code_indicators):
                    # This might be code that needs proper formatting
                    if not line.strip().startswith('```'):
                        # Wrap in code block if not already
                        pass  # Let the postprocessing handle this

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _fix_section_hierarchy(self, content: str) -> str:
        """Fix markdown section hierarchy."""
        lines = content.split('\n')
        fixed_lines = []
        header_level = 0

        for line in lines:
            if line.strip().startswith('#'):
                current_level = len(line) - len(line.lstrip('#'))
                if header_level > 0 and current_level > header_level + 1:
                    line = '#' * (header_level + 1) + line[current_level:]
                header_level = len(line) - len(line.lstrip('#'))

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_installation_steps(self, content: str) -> str:
        """Format installation and setup steps consistently."""
        lines = content.split('\n')
        fixed_lines = []
        in_setup_section = False
        step_counter = 1

        for line in lines:
            if ('setup' in line.lower() or 'installation' in line.lower()) and line.strip().startswith('#'):
                in_setup_section = True
                step_counter = 1
            elif line.strip().startswith('#') and in_setup_section:
                in_setup_section = False

            if in_setup_section:
                # Convert bullet points to numbered steps
                if re.match(r'^\s*[-*]', line):
                    step_text = re.sub(r'^\s*[-*]\s*', '', line)
                    line = f"{step_counter}. {step_text}"
                    step_counter += 1

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_code_examples(self, content: str) -> str:
        """Ensure code examples are properly formatted."""
        lines = content.split('\n')
        fixed_lines = []
        in_example_section = False

        for line in lines:
            if 'example' in line.lower() and line.strip().startswith('#'):
                in_example_section = True
            elif line.strip().startswith('#') and in_example_section:
                in_example_section = False

            if in_example_section:
                # Add language hints to code blocks
                if line.strip() == '```':
                    # Try to detect language from context
                    next_lines = lines[lines.index(line)+1:lines.index(line)+5]
                    if any('import ' in l or 'from ' in l for l in next_lines):
                        line = '```python'
                    elif any(('function' in l or 'const ' in l or 'let ' in l) for l in next_lines):
                        line = '```javascript'
                    elif any('def ' in l or 'class ' in l for l in next_lines):
                        line = '```python'

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_troubleshooting(self, content: str) -> str:
        """Format troubleshooting sections consistently."""
        lines = content.split('\n')
        fixed_lines = []
        in_troubleshooting = False

        for line in lines:
            if 'troubleshooting' in line.lower() and line.strip().startswith('#'):
                in_troubleshooting = True
            elif line.strip().startswith('#') and in_troubleshooting:
                in_troubleshooting = False

            if in_troubleshooting:
                # Format problem/solution pairs
                if 'problem:' in line.lower() or 'issue:' in line.lower():
                    if not line.strip().startswith('⚠️'):
                        line = f"⚠️ **Problem**: {line.strip()}"

                elif 'solution:' in line.lower() or 'fix:' in line.lower():
                    if not line.strip().startswith('✅'):
                        line = f"✅ **Solution**: {line.strip()}"

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)