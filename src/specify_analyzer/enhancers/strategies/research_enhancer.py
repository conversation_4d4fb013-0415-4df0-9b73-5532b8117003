"""ResearchEnhancer for improving technology research documentation."""

import re
from typing import List, Dict, Any
from .base_strategy import BaseEnhancementStrategy
from ..base import DocumentType


class ResearchEnhancer(BaseEnhancementStrategy):
    """Enhancement strategy for research.md documents."""

    def __init__(self):
        super().__init__(DocumentType.RESEARCH)

    def get_sections_to_enhance(self, content: str) -> List[str]:
        """Identify sections that need enhancement in research documents."""
        sections = []

        if "comparison" in content.lower() or "alternatives" in content.lower():
            sections.append("Technology Comparison")
        if "architecture" in content.lower() or "patterns" in content.lower():
            sections.append("Architecture Patterns")
        if "performance" in content.lower() or "benchmark" in content.lower():
            sections.append("Performance Analysis")
        if "future" in content.lower() or "trends" in content.lower():
            sections.append("Future Considerations")
        if "decision" in content.lower() or "rationale" in content.lower():
            sections.append("Decision Rationale")

        return sections

    def preprocess_content(self, content: str) -> str:
        """Preprocess research content before enhancement."""
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = self._standardize_technology_names(content)
        return content.strip()

    def postprocess_content(self, enhanced_content: str) -> str:
        """Postprocess enhanced research content."""
        enhanced_content = self._fix_section_hierarchy(enhanced_content)
        enhanced_content = self._format_pros_cons(enhanced_content)
        enhanced_content = self._format_comparisons(enhanced_content)
        enhanced_content = self._format_decision_factors(enhanced_content)
        return enhanced_content

    def _standardize_technology_names(self, content: str) -> str:
        """Standardize technology names for consistency."""
        tech_replacements = {
            'nodejs': 'Node.js',
            'reactjs': 'React',
            'vuejs': 'Vue.js',
            'angularjs': 'Angular',
            'javascript': 'JavaScript',
            'typescript': 'TypeScript',
            'python': 'Python',
            'golang': 'Go',
            'docker': 'Docker',
            'kubernetes': 'Kubernetes',
            'postgresql': 'PostgreSQL',
            'mysql': 'MySQL',
            'mongodb': 'MongoDB',
            'elasticsearch': 'Elasticsearch',
            'redis': 'Redis',
            'nginx': 'Nginx',
            'apache': 'Apache',
            'aws': 'AWS',
            'gcp': 'Google Cloud Platform',
            'azure': 'Microsoft Azure'
        }

        for old, new in tech_replacements.items():
            content = re.sub(rf'\b{old}\b', new, content, flags=re.IGNORECASE)

        return content

    def _fix_section_hierarchy(self, content: str) -> str:
        """Fix markdown section hierarchy."""
        lines = content.split('\n')
        fixed_lines = []
        header_level = 0

        for line in lines:
            if line.strip().startswith('#'):
                current_level = len(line) - len(line.lstrip('#'))
                if header_level > 0 and current_level > header_level + 1:
                    line = '#' * (header_level + 1) + line[current_level:]
                header_level = len(line) - len(line.lstrip('#'))

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_pros_cons(self, content: str) -> str:
        """Format pros and cons sections consistently."""
        lines = content.split('\n')
        fixed_lines = []
        in_pros_cons = False

        for line in lines:
            if ('pros' in line.lower() and 'cons' in line.lower()) or \
               ('advantages' in line.lower() and 'disadvantages' in line.lower()):
                in_pros_cons = True
            elif line.strip().startswith('#') and in_pros_cons:
                in_pros_cons = False

            if in_pros_cons:
                # Format pros
                if 'pro:' in line.lower() or line.strip().startswith('+'):
                    if not line.strip().startswith('✅'):
                        line = re.sub(r'^\s*[+]?\s*pro:?\s*', '✅ **Pro**: ', line, flags=re.IGNORECASE)

                # Format cons
                elif 'con:' in line.lower() or line.strip().startswith('-'):
                    if not line.strip().startswith('❌'):
                        line = re.sub(r'^\s*[-]?\s*con:?\s*', '❌ **Con**: ', line, flags=re.IGNORECASE)

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_comparisons(self, content: str) -> str:
        """Format technology comparison tables and sections."""
        # Look for comparison indicators
        comparison_indicators = ['vs', 'versus', 'compared to', 'against']

        lines = content.split('\n')
        fixed_lines = []

        for line in lines:
            for indicator in comparison_indicators:
                if indicator in line.lower() and not line.strip().startswith('|'):
                    # Format as comparison item
                    if not line.strip().startswith('-') and not line.strip().startswith('*'):
                        line = f"- {line.strip()}"
                    break

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_decision_factors(self, content: str) -> str:
        """Format decision factors and criteria."""
        factor_keywords = [
            'factor', 'criteria', 'consideration', 'requirement',
            'constraint', 'priority', 'goal', 'objective'
        ]

        lines = content.split('\n')
        fixed_lines = []
        in_decision_section = False

        for line in lines:
            if 'decision' in line.lower() and line.strip().startswith('#'):
                in_decision_section = True
            elif line.strip().startswith('#') and in_decision_section:
                in_decision_section = False

            if in_decision_section:
                for keyword in factor_keywords:
                    if keyword in line.lower() and not line.strip().startswith('#'):
                        if not line.strip().startswith('-') and not line.strip().startswith('*'):
                            line = f"- **{keyword.title()}**: {line.strip()}"
                        break

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)