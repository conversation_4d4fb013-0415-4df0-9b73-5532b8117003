"""PlanEnhancer for improving technical implementation plans."""

import re
from typing import List, Dict, Any
from .base_strategy import BaseEnhancementStrategy
from ..base import DocumentType


class PlanEnhancer(BaseEnhancementStrategy):
    """Enhancement strategy for plan.md documents."""

    def __init__(self):
        super().__init__(DocumentType.PLAN)

    def get_sections_to_enhance(self, content: str) -> List[str]:
        """Identify sections that need enhancement in plan documents."""
        sections = []

        # Look for key plan sections
        if "architecture" in content.lower():
            sections.append("Architecture")
        if "technology" in content.lower() or "tech stack" in content.lower():
            sections.append("Technology Stack")
        if "development" in content.lower() or "phases" in content.lower():
            sections.append("Development Phases")
        if "infrastructure" in content.lower() or "deployment" in content.lower():
            sections.append("Infrastructure")
        if "risk" in content.lower():
            sections.append("Risk Management")
        if "testing" in content.lower():
            sections.append("Testing Strategy")

        return sections

    def preprocess_content(self, content: str) -> str:
        """Preprocess plan content before enhancement."""
        # Clean up whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

        # Standardize technology mentions
        content = self._standardize_technology_references(content)

        return content.strip()

    def postprocess_content(self, enhanced_content: str) -> str:
        """Postprocess enhanced plan content."""
        # Ensure proper section hierarchy
        enhanced_content = self._fix_section_hierarchy(enhanced_content)

        # Format technology comparisons
        enhanced_content = self._format_technology_comparisons(enhanced_content)

        # Ensure risk assessments are properly formatted
        enhanced_content = self._format_risk_assessments(enhanced_content)

        return enhanced_content

    def _standardize_technology_references(self, content: str) -> str:
        """Standardize how technologies are referenced."""
        # Common technology replacements for consistency
        replacements = {
            'nodejs': 'Node.js',
            'node.js': 'Node.js',
            'reactjs': 'React',
            'vuejs': 'Vue.js',
            'angularjs': 'Angular',
            'javascript': 'JavaScript',
            'typescript': 'TypeScript',
            'python': 'Python',
            'golang': 'Go',
            'docker': 'Docker',
            'kubernetes': 'Kubernetes',
            'postgresql': 'PostgreSQL',
            'mysql': 'MySQL',
            'mongodb': 'MongoDB',
            'redis': 'Redis'
        }

        for old, new in replacements.items():
            content = re.sub(rf'\b{old}\b', new, content, flags=re.IGNORECASE)

        return content

    def _fix_section_hierarchy(self, content: str) -> str:
        """Fix markdown section hierarchy for plan documents."""
        lines = content.split('\n')
        fixed_lines = []
        header_level = 0

        for line in lines:
            if line.strip().startswith('#'):
                current_level = len(line) - len(line.lstrip('#'))

                # Ensure proper hierarchy
                if header_level > 0 and current_level > header_level + 1:
                    line = '#' * (header_level + 1) + line[current_level:]

                header_level = len(line) - len(line.lstrip('#'))

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_technology_comparisons(self, content: str) -> str:
        """Format technology comparison sections."""
        lines = content.split('\n')
        fixed_lines = []
        in_comparison = False

        for line in lines:
            if 'comparison' in line.lower() or 'alternatives' in line.lower():
                in_comparison = True
            elif line.strip().startswith('#') and in_comparison:
                in_comparison = False

            if in_comparison and ('vs' in line.lower() or 'compared to' in line.lower()):
                if not line.strip().startswith('|') and not line.strip().startswith('-'):
                    line = f"- {line.strip()}"

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_risk_assessments(self, content: str) -> str:
        """Format risk assessment sections."""
        lines = content.split('\n')
        fixed_lines = []
        in_risk_section = False

        for line in lines:
            if 'risk' in line.lower() and line.strip().startswith('#'):
                in_risk_section = True
            elif line.strip().startswith('#') and in_risk_section:
                in_risk_section = False

            if in_risk_section:
                # Format risk items
                if ('high' in line.lower() or 'medium' in line.lower() or 'low' in line.lower()) and 'risk' in line.lower():
                    if not line.strip().startswith('-') and not line.strip().startswith('*'):
                        line = f"- **Risk**: {line.strip()}"

                # Format mitigation items
                if 'mitigation' in line.lower():
                    if not line.strip().startswith('-') and not line.strip().startswith('*'):
                        line = f"  - **Mitigation**: {line.strip()}"

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)