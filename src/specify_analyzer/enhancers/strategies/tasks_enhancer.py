"""TasksEnhancer for improving task breakdown documentation."""

import re
from typing import List, Dict, Any
from .base_strategy import BaseEnhancementStrategy
from ..base import DocumentType


class TasksEnhancer(BaseEnhancementStrategy):
    """Enhancement strategy for tasks.md documents."""

    def __init__(self):
        super().__init__(DocumentType.TASKS)

    def get_sections_to_enhance(self, content: str) -> List[str]:
        """Identify sections that need enhancement in tasks documents."""
        sections = []

        if "phases" in content.lower() or "breakdown" in content.lower():
            sections.append("Task Breakdown")
        if "dependencies" in content.lower():
            sections.append("Dependencies")
        if "estimates" in content.lower() or "time" in content.lower():
            sections.append("Time Estimates")
        if "parallel" in content.lower() or "[p]" in content.lower():
            sections.append("Parallel Execution")
        if "acceptance" in content.lower() or "criteria" in content.lower():
            sections.append("Acceptance Criteria")

        return sections

    def preprocess_content(self, content: str) -> str:
        """Preprocess tasks content before enhancement."""
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = self._standardize_task_format(content)
        return content.strip()

    def postprocess_content(self, enhanced_content: str) -> str:
        """Postprocess enhanced tasks content."""
        enhanced_content = self._fix_section_hierarchy(enhanced_content)
        enhanced_content = self._format_task_dependencies(enhanced_content)
        enhanced_content = self._format_time_estimates(enhanced_content)
        enhanced_content = self._mark_parallel_tasks(enhanced_content)
        return enhanced_content

    def _standardize_task_format(self, content: str) -> str:
        """Standardize task numbering and format."""
        lines = content.split('\n')
        fixed_lines = []
        task_counter = 1

        for line in lines:
            # Detect task items (numbered or bulleted)
            if re.match(r'^\s*[\d\w]+[\.\)]', line) or re.match(r'^\s*[-*]', line):
                # Standardize to numbered format
                task_text = re.sub(r'^\s*[\d\w]*[\.\)\-\*]\s*', '', line)
                line = f"{task_counter}. {task_text}"
                task_counter += 1

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _fix_section_hierarchy(self, content: str) -> str:
        """Fix markdown section hierarchy."""
        lines = content.split('\n')
        fixed_lines = []
        header_level = 0

        for line in lines:
            if line.strip().startswith('#'):
                current_level = len(line) - len(line.lstrip('#'))
                if header_level > 0 and current_level > header_level + 1:
                    line = '#' * (header_level + 1) + line[current_level:]
                header_level = len(line) - len(line.lstrip('#'))

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_task_dependencies(self, content: str) -> str:
        """Format task dependencies consistently."""
        dependency_patterns = [
            (r'depends on', 'Depends on:'),
            (r'requires', 'Requires:'),
            (r'after', 'After:'),
            (r'blocks', 'Blocks:'),
            (r'blocked by', 'Blocked by:')
        ]

        for pattern, replacement in dependency_patterns:
            content = re.sub(
                rf'\b{pattern}\b',
                replacement,
                content,
                flags=re.IGNORECASE
            )

        return content

    def _format_time_estimates(self, content: str) -> str:
        """Format time estimates consistently."""
        # Standardize time units
        time_patterns = [
            (r'(\d+)\s*hrs?\b', r'\1 hours'),
            (r'(\d+)\s*days?\b', r'\1 days'),
            (r'(\d+)\s*weeks?\b', r'\1 weeks'),
            (r'(\d+)\s*mins?\b', r'\1 minutes')
        ]

        for pattern, replacement in time_patterns:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

        return content

    def _mark_parallel_tasks(self, content: str) -> str:
        """Mark tasks that can be executed in parallel."""
        lines = content.split('\n')
        fixed_lines = []

        for line in lines:
            # Look for tasks that can be parallelized
            if (re.match(r'^\s*\d+\.|^\s*[-*]', line) and
                any(keyword in line.lower() for keyword in
                    ['independent', 'parallel', 'concurrent', 'simultaneously'])):
                if '[P]' not in line:
                    line = line.rstrip() + ' [P]'

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)