"""DataModelEnhancer for improving data model documentation."""

import re
from typing import List, Dict, Any
from .base_strategy import BaseEnhancementStrategy
from ..base import DocumentType


class DataModelEnhancer(BaseEnhancementStrategy):
    """Enhancement strategy for data-model.md documents."""

    def __init__(self):
        super().__init__(DocumentType.DATA_MODEL)

    def get_sections_to_enhance(self, content: str) -> List[str]:
        """Identify sections that need enhancement in data model documents."""
        sections = []

        if "entities" in content.lower() or "models" in content.lower():
            sections.append("Entities")
        if "relationships" in content.lower():
            sections.append("Relationships")
        if "validation" in content.lower():
            sections.append("Validation")
        if "data flow" in content.lower():
            sections.append("Data Flow")
        if "storage" in content.lower() or "database" in content.lower():
            sections.append("Storage Strategy")

        return sections

    def preprocess_content(self, content: str) -> str:
        """Preprocess data model content before enhancement."""
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = self._standardize_field_definitions(content)
        return content.strip()

    def postprocess_content(self, enhanced_content: str) -> str:
        """Postprocess enhanced data model content."""
        enhanced_content = self._fix_section_hierarchy(enhanced_content)
        enhanced_content = self._format_entity_definitions(enhanced_content)
        enhanced_content = self._format_relationships(enhanced_content)
        return enhanced_content

    def _standardize_field_definitions(self, content: str) -> str:
        """Standardize field definition format."""
        field_types = {
            'str': 'String',
            'int': 'Integer',
            'bool': 'Boolean',
            'datetime': 'DateTime',
            'date': 'Date',
            'time': 'Time',
            'float': 'Float',
            'decimal': 'Decimal',
            'text': 'Text',
            'json': 'JSON'
        }

        for old_type, new_type in field_types.items():
            content = re.sub(rf'\b{old_type}\b', new_type, content, flags=re.IGNORECASE)

        return content

    def _fix_section_hierarchy(self, content: str) -> str:
        """Fix markdown section hierarchy."""
        lines = content.split('\n')
        fixed_lines = []
        header_level = 0

        for line in lines:
            if line.strip().startswith('#'):
                current_level = len(line) - len(line.lstrip('#'))
                if header_level > 0 and current_level > header_level + 1:
                    line = '#' * (header_level + 1) + line[current_level:]
                header_level = len(line) - len(line.lstrip('#'))

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_entity_definitions(self, content: str) -> str:
        """Format entity definitions consistently."""
        lines = content.split('\n')
        fixed_lines = []
        in_entity_section = False

        for line in lines:
            if 'entity' in line.lower() or 'model' in line.lower():
                if line.strip().startswith('#'):
                    in_entity_section = True
            elif line.strip().startswith('#') and in_entity_section:
                in_entity_section = False

            if in_entity_section and ':' in line and not line.strip().startswith('#'):
                if not line.strip().startswith('-') and not line.strip().startswith('*'):
                    line = f"- {line.strip()}"

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_relationships(self, content: str) -> str:
        """Format relationship definitions."""
        relationship_patterns = [
            (r'one[- ]to[- ]one', 'One-to-One'),
            (r'one[- ]to[- ]many', 'One-to-Many'),
            (r'many[- ]to[- ]one', 'Many-to-One'),
            (r'many[- ]to[- ]many', 'Many-to-Many')
        ]

        for pattern, replacement in relationship_patterns:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

        return content