"""SpecEnhancer for improving requirements documentation."""

import re
from typing import List, Dict, Any
from .base_strategy import BaseEnhancementStrategy
from ..base import DocumentType


class SpecEnhancer(BaseEnhancementStrategy):
    """Enhancement strategy for spec.md documents."""

    def __init__(self):
        super().__init__(DocumentType.SPEC)

    def get_sections_to_enhance(self, content: str) -> List[str]:
        """Identify sections that need enhancement in spec documents."""
        sections = []

        # Look for key spec sections
        if "functional requirements" in content.lower():
            sections.append("Functional Requirements")
        if "non-functional requirements" in content.lower():
            sections.append("Non-Functional Requirements")
        if "user stories" in content.lower():
            sections.append("User Stories")
        if "api" in content.lower():
            sections.append("API Specifications")
        if "interface" in content.lower():
            sections.append("User Interface")
        if "business" in content.lower():
            sections.append("Business Logic")

        return sections

    def preprocess_content(self, content: str) -> str:
        """Preprocess spec content before enhancement."""
        # Clean up whitespace and ensure proper formatting
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)

        # Ensure user stories follow proper format
        content = self._standardize_user_stories(content)

        return content.strip()

    def postprocess_content(self, enhanced_content: str) -> str:
        """Postprocess enhanced spec content."""
        # Ensure proper section hierarchy
        enhanced_content = self._fix_section_hierarchy(enhanced_content)

        # Validate user story format
        enhanced_content = self._validate_user_stories(enhanced_content)

        # Ensure acceptance criteria are properly formatted
        enhanced_content = self._format_acceptance_criteria(enhanced_content)

        return enhanced_content

    def _standardize_user_stories(self, content: str) -> str:
        """Standardize user story format."""
        # Pattern for user stories
        story_pattern = r'(As an? .+?, I want .+?, so that .+?\.)'

        def format_story(match):
            story = match.group(1)
            return f"**User Story**: {story}"

        return re.sub(story_pattern, format_story, content, flags=re.IGNORECASE)

    def _fix_section_hierarchy(self, content: str) -> str:
        """Fix markdown section hierarchy."""
        lines = content.split('\n')
        fixed_lines = []
        header_level = 0

        for line in lines:
            if line.strip().startswith('#'):
                # Count current header level
                current_level = len(line) - len(line.lstrip('#'))

                # Ensure proper hierarchy (no jumping from ## to ####)
                if header_level > 0 and current_level > header_level + 1:
                    line = '#' * (header_level + 1) + line[current_level:]

                header_level = len(line) - len(line.lstrip('#'))

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _validate_user_stories(self, content: str) -> str:
        """Validate and fix user story format."""
        lines = content.split('\n')
        in_user_stories_section = False
        fixed_lines = []

        for line in lines:
            if 'user stories' in line.lower() and line.strip().startswith('#'):
                in_user_stories_section = True
            elif line.strip().startswith('#') and in_user_stories_section:
                in_user_stories_section = False

            if in_user_stories_section and 'as a' in line.lower():
                # Ensure proper user story format
                if not line.strip().startswith('**User Story**'):
                    line = f"**User Story**: {line.strip()}"

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)

    def _format_acceptance_criteria(self, content: str) -> str:
        """Format acceptance criteria consistently."""
        # Look for acceptance criteria patterns
        criteria_pattern = r'(acceptance criteria|given|when|then)'

        lines = content.split('\n')
        fixed_lines = []

        for line in lines:
            if re.search(criteria_pattern, line.lower()):
                if not line.strip().startswith('-') and not line.strip().startswith('*'):
                    line = f"- {line.strip()}"

            fixed_lines.append(line)

        return '\n'.join(fixed_lines)