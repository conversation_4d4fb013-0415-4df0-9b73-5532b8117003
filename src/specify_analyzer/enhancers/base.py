"""Base classes and interfaces for AI providers."""

import os
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any
import json


class EnhancementLevel(Enum):
    """Enhancement intensity levels."""
    LIGHT = "light"
    MODERATE = "moderate"
    COMPREHENSIVE = "comprehensive"


class DocumentType(Enum):
    """Spec Kit document types."""
    SPEC = "spec"
    PLAN = "plan"
    DATA_MODEL = "data_model"
    TASKS = "tasks"
    RESEARCH = "research"
    QUICKSTART = "quickstart"


@dataclass
class EnhancementConfig:
    """Configuration for AI enhancement."""
    provider: str
    model: str
    level: EnhancementLevel
    max_tokens: int = 4000
    temperature: float = 0.3
    api_key: Optional[str] = None
    timeout: int = 60
    retry_attempts: int = 3
    cost_limit: Optional[float] = None
    sections_to_enhance: Optional[List[str]] = None
    custom_prompts: Optional[Dict[str, str]] = None


@dataclass
class EnhancementResult:
    """Result of AI enhancement operation."""
    success: bool
    enhanced_content: Optional[str] = None
    original_content: Optional[str] = None
    provider_used: Optional[str] = None
    model_used: Optional[str] = None
    tokens_used: int = 0
    cost_estimate: float = 0.0
    processing_time: float = 0.0
    error_message: Optional[str] = None
    sections_enhanced: Optional[List[str]] = None


class RateLimiter:
    """Simple rate limiter for API calls."""

    def __init__(self, max_requests_per_minute: int = 60):
        self.max_requests = max_requests_per_minute
        self.requests = []

    def wait_if_needed(self):
        """Wait if rate limit would be exceeded."""
        now = time.time()
        # Remove requests older than 1 minute
        self.requests = [req_time for req_time in self.requests if now - req_time < 60]

        if len(self.requests) >= self.max_requests:
            sleep_time = 60 - (now - self.requests[0])
            if sleep_time > 0:
                time.sleep(sleep_time)

        self.requests.append(now)


class BaseAIProvider(ABC):
    """Abstract base class for AI providers."""

    def __init__(self, config: EnhancementConfig):
        self.config = config
        self.rate_limiter = RateLimiter()
        self._validate_config()

    def _validate_config(self):
        """Validate provider configuration."""
        if not self.config.api_key and self._requires_api_key():
            env_var = self._get_api_key_env_var()
            self.config.api_key = os.getenv(env_var)
            if not self.config.api_key:
                raise ValueError(f"API key required. Set {env_var} environment variable.")

    @abstractmethod
    def _requires_api_key(self) -> bool:
        """Return True if this provider requires an API key."""
        pass

    @abstractmethod
    def _get_api_key_env_var(self) -> str:
        """Return the environment variable name for the API key."""
        pass

    @abstractmethod
    def _estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Estimate the cost for the given token usage."""
        pass

    @abstractmethod
    def _make_api_call(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """Make the actual API call to the provider."""
        pass

    def enhance_document(
        self,
        content: str,
        document_type: DocumentType,
        project_context: Dict[str, Any],
        enhancement_prompt: str
    ) -> EnhancementResult:
        """Enhance a document using AI."""
        start_time = time.time()

        try:
            # Check rate limiting
            self.rate_limiter.wait_if_needed()

            # Prepare messages
            messages = self._prepare_messages(
                content, document_type, project_context, enhancement_prompt
            )

            # Estimate cost before making call
            input_tokens = self._estimate_tokens(" ".join([msg["content"] for msg in messages]))
            estimated_cost = self._estimate_cost(input_tokens, self.config.max_tokens)

            if self.config.cost_limit and estimated_cost > self.config.cost_limit:
                return EnhancementResult(
                    success=False,
                    error_message=f"Estimated cost ${estimated_cost:.4f} exceeds limit ${self.config.cost_limit:.4f}"
                )

            # Make API call with retries
            for attempt in range(self.config.retry_attempts):
                try:
                    response = self._make_api_call(messages)
                    enhanced_content = self._extract_content(response)
                    tokens_used = self._extract_token_usage(response)
                    actual_cost = self._estimate_cost(input_tokens, tokens_used)

                    return EnhancementResult(
                        success=True,
                        enhanced_content=enhanced_content,
                        original_content=content,
                        provider_used=self.config.provider,
                        model_used=self.config.model,
                        tokens_used=tokens_used,
                        cost_estimate=actual_cost,
                        processing_time=time.time() - start_time
                    )

                except Exception as e:
                    if attempt == self.config.retry_attempts - 1:
                        raise e
                    time.sleep(2 ** attempt)  # Exponential backoff

        except Exception as e:
            return EnhancementResult(
                success=False,
                original_content=content,
                error_message=str(e),
                processing_time=time.time() - start_time
            )

    def _prepare_messages(
        self,
        content: str,
        document_type: DocumentType,
        project_context: Dict[str, Any],
        enhancement_prompt: str
    ) -> List[Dict[str, str]]:
        """Prepare messages for the AI provider."""
        system_prompt = f"""You are an expert technical writer specializing in software documentation.
Your task is to enhance {document_type.value} documentation for a software project.

Project Context:
- Language: {project_context.get('primary_language', 'Unknown')}
- Frameworks: {', '.join(project_context.get('frameworks', []))}
- Architecture: {project_context.get('architecture_type', 'Unknown')}
- Project Type: {project_context.get('project_type', 'Unknown')}

Enhancement Level: {self.config.level.value}
Max Output Tokens: {self.config.max_tokens}

Guidelines:
1. Preserve the original structure and format
2. Enhance clarity, completeness, and technical accuracy
3. Add missing details while maintaining conciseness
4. Ensure consistency with project context
5. Use markdown formatting appropriately
"""

        user_prompt = f"""{enhancement_prompt}

Original Document:
{content}

Please enhance this document according to the guidelines above."""

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _estimate_tokens(self, text: str) -> int:
        """Rough estimation of tokens (4 chars ≈ 1 token)."""
        return len(text) // 4

    @abstractmethod
    def _extract_content(self, response: Dict[str, Any]) -> str:
        """Extract enhanced content from API response."""
        pass

    @abstractmethod
    def _extract_token_usage(self, response: Dict[str, Any]) -> int:
        """Extract token usage from API response."""
        pass