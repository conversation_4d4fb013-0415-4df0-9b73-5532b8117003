"""Enhancement pipeline orchestrator."""

import os
import json
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from .base import BaseAIProvider, EnhancementConfig, EnhancementResult, DocumentType, EnhancementLevel
from .providers import OpenAIProvider, AnthropicProvider, GoogleProvider, OllamaProvider
from .strategies import (
    SpecEnhancer, PlanEnhancer, DataModelEnhancer,
    TasksEnhancer, ResearchEnhancer, QuickstartEnhancer
)


@dataclass
class EnhancementCache:
    """Cache entry for enhanced documents."""
    content_hash: str
    enhanced_content: str
    provider: str
    model: str
    level: str
    timestamp: float


class EnhancementPipeline:
    """Orchestrates the enhancement of all Spec Kit documents."""

    def __init__(self, config: EnhancementConfig, cache_dir: Optional[Path] = None):
        self.config = config
        self.cache_dir = cache_dir or Path.home() / '.specify' / 'enhancement_cache'
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Initialize provider
        self.provider = self._create_provider()

        # Initialize strategies
        self.strategies = {
            DocumentType.SPEC: SpecEnhancer(),
            DocumentType.PLAN: PlanEnhancer(),
            DocumentType.DATA_MODEL: DataModelEnhancer(),
            DocumentType.TASKS: TasksEnhancer(),
            DocumentType.RESEARCH: ResearchEnhancer(),
            DocumentType.QUICKSTART: QuickstartEnhancer(),
        }

    def _create_provider(self) -> BaseAIProvider:
        """Create AI provider based on configuration."""
        provider_classes = {
            'openai': OpenAIProvider,
            'anthropic': AnthropicProvider,
            'google': GoogleProvider,
            'ollama': OllamaProvider,
        }

        provider_class = provider_classes.get(self.config.provider.lower())
        if not provider_class:
            raise ValueError(f"Unsupported provider: {self.config.provider}")

        return provider_class(self.config)

    def enhance_documents(
        self,
        documents: Dict[str, str],
        project_context: Dict[str, Any],
        progress_callback: Optional[callable] = None
    ) -> Dict[str, EnhancementResult]:
        """Enhance multiple documents with consistency checks."""
        results = {}
        total_cost = 0.0

        # Map file names to document types
        doc_type_mapping = {
            'spec.md': DocumentType.SPEC,
            'plan.md': DocumentType.PLAN,
            'data-model.md': DocumentType.DATA_MODEL,
            'tasks.md': DocumentType.TASKS,
            'research.md': DocumentType.RESEARCH,
            'quickstart.md': DocumentType.QUICKSTART,
        }

        # Estimate total cost first
        if self.config.cost_limit:
            estimated_cost = self._estimate_total_cost(documents, project_context)
            if estimated_cost > self.config.cost_limit:
                raise ValueError(
                    f"Estimated cost ${estimated_cost:.4f} exceeds limit ${self.config.cost_limit:.4f}"
                )

        # Process documents in dependency order
        processing_order = [
            DocumentType.SPEC,  # Foundation requirements
            DocumentType.PLAN,  # Technical implementation
            DocumentType.DATA_MODEL,  # Data structures
            DocumentType.TASKS,  # Implementation breakdown
            DocumentType.RESEARCH,  # Technology decisions
            DocumentType.QUICKSTART,  # User documentation
        ]

        for i, doc_type in enumerate(processing_order):
            # Find matching document
            doc_name = None
            content = None
            for name, doc_content in documents.items():
                if doc_type_mapping.get(name) == doc_type:
                    doc_name = name
                    content = doc_content
                    break

            if not content:
                continue  # Skip if document not found

            if progress_callback:
                progress_callback(f"Enhancing {doc_name}", i, len(processing_order))

            # Check cache first
            cached_result = self._get_cached_result(content, doc_type)
            if cached_result:
                results[doc_name] = cached_result
                continue

            # Enhance document
            strategy = self.strategies[doc_type]
            enhanced_context = self._build_enhanced_context(
                project_context, results, doc_type
            )

            result = strategy.enhance_document(
                self.provider,
                content,
                enhanced_context
            )

            if result.success:
                total_cost += result.cost_estimate
                # Cache the result
                self._cache_result(content, doc_type, result)

                # Validate consistency with other documents
                validation_issues = self._validate_consistency(
                    doc_type, result.enhanced_content, results
                )
                if validation_issues:
                    result.error_message = f"Consistency issues: {', '.join(validation_issues)}"

            results[doc_name] = result

            # Check cost limit
            if self.config.cost_limit and total_cost > self.config.cost_limit:
                remaining_docs = len(processing_order) - i - 1
                if remaining_docs > 0:
                    for j in range(i + 1, len(processing_order)):
                        next_doc_type = processing_order[j]
                        next_doc_name = None
                        for name in documents.keys():
                            if doc_type_mapping.get(name) == next_doc_type:
                                next_doc_name = name
                                break
                        if next_doc_name:
                            results[next_doc_name] = EnhancementResult(
                                success=False,
                                error_message=f"Skipped due to cost limit (${total_cost:.4f})"
                            )
                break

        return results

    def enhance_single_document(
        self,
        content: str,
        document_type: DocumentType,
        project_context: Dict[str, Any]
    ) -> EnhancementResult:
        """Enhance a single document."""
        # Check cache first
        cached_result = self._get_cached_result(content, document_type)
        if cached_result:
            return cached_result

        strategy = self.strategies[document_type]
        result = strategy.enhance_document(self.provider, content, project_context)

        if result.success:
            self._cache_result(content, document_type, result)

        return result

    def _estimate_total_cost(
        self,
        documents: Dict[str, str],
        project_context: Dict[str, Any]
    ) -> float:
        """Estimate total cost for enhancing all documents."""
        total_cost = 0.0

        for content in documents.values():
            input_tokens = len(content) // 4  # Rough estimation
            output_tokens = self.config.max_tokens
            total_cost += self.provider._estimate_cost(input_tokens, output_tokens)

        return total_cost

    def _build_enhanced_context(
        self,
        base_context: Dict[str, Any],
        previous_results: Dict[str, EnhancementResult],
        current_doc_type: DocumentType
    ) -> Dict[str, Any]:
        """Build context including information from previously enhanced documents."""
        enhanced_context = base_context.copy()

        # Add insights from previously enhanced documents
        if previous_results:
            enhanced_context['previous_enhancements'] = {}
            for doc_name, result in previous_results.items():
                if result.success and result.enhanced_content:
                    enhanced_context['previous_enhancements'][doc_name] = {
                        'sections_enhanced': result.sections_enhanced or [],
                        'content_length': len(result.enhanced_content)
                    }

        return enhanced_context

    def _validate_consistency(
        self,
        doc_type: DocumentType,
        content: str,
        previous_results: Dict[str, EnhancementResult]
    ) -> List[str]:
        """Validate consistency between documents."""
        issues = []

        # Basic consistency checks
        if doc_type == DocumentType.TASKS and previous_results:
            # Check if tasks align with requirements from spec
            spec_result = None
            for name, result in previous_results.items():
                if 'spec.md' in name and result.success:
                    spec_result = result
                    break

            if spec_result and spec_result.enhanced_content:
                # Simple check: ensure major requirements are reflected in tasks
                spec_content = spec_result.enhanced_content.lower()
                task_content = content.lower()

                if 'api' in spec_content and 'api' not in task_content:
                    issues.append("API requirements not reflected in tasks")

                if 'database' in spec_content and 'database' not in task_content:
                    issues.append("Database requirements not reflected in tasks")

        return issues

    def _get_cache_key(self, content: str, doc_type: DocumentType) -> str:
        """Generate cache key for content."""
        content_hash = hashlib.md5(content.encode()).hexdigest()
        config_str = f"{self.config.provider}-{self.config.model}-{self.config.level.value}"
        config_hash = hashlib.md5(config_str.encode()).hexdigest()
        return f"{doc_type.value}-{content_hash}-{config_hash}"

    def _get_cached_result(
        self,
        content: str,
        doc_type: DocumentType
    ) -> Optional[EnhancementResult]:
        """Get cached enhancement result if available."""
        cache_key = self._get_cache_key(content, doc_type)
        cache_file = self.cache_dir / f"{cache_key}.json"

        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                cache_entry = EnhancementCache(**cache_data)

                # Verify content hasn't changed
                current_hash = hashlib.md5(content.encode()).hexdigest()
                if cache_entry.content_hash == current_hash:
                    return EnhancementResult(
                        success=True,
                        enhanced_content=cache_entry.enhanced_content,
                        original_content=content,
                        provider_used=cache_entry.provider,
                        model_used=cache_entry.model,
                        tokens_used=0,
                        cost_estimate=0.0,
                        processing_time=0.0
                    )
            except (json.JSONDecodeError, TypeError, KeyError):
                # Cache file corrupted, remove it
                cache_file.unlink(missing_ok=True)

        return None

    def _cache_result(
        self,
        content: str,
        doc_type: DocumentType,
        result: EnhancementResult
    ):
        """Cache enhancement result."""
        if not result.success or not result.enhanced_content:
            return

        cache_key = self._get_cache_key(content, doc_type)
        cache_file = self.cache_dir / f"{cache_key}.json"

        cache_entry = EnhancementCache(
            content_hash=hashlib.md5(content.encode()).hexdigest(),
            enhanced_content=result.enhanced_content,
            provider=result.provider_used or self.config.provider,
            model=result.model_used or self.config.model,
            level=self.config.level.value,
            timestamp=result.processing_time
        )

        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_entry.__dict__, f, indent=2)
        except Exception:
            # Silently fail cache writes
            pass

    def clear_cache(self):
        """Clear all cached enhancement results."""
        if self.cache_dir.exists():
            for cache_file in self.cache_dir.glob("*.json"):
                cache_file.unlink(missing_ok=True)

    def generate_diff(self, original: str, enhanced: str) -> str:
        """Generate a simple diff between original and enhanced content."""
        original_lines = original.split('\n')
        enhanced_lines = enhanced.split('\n')

        diff_lines = []
        diff_lines.append("--- Original")
        diff_lines.append("+++ Enhanced")

        # Simple line-by-line comparison
        max_lines = max(len(original_lines), len(enhanced_lines))

        for i in range(max_lines):
            orig_line = original_lines[i] if i < len(original_lines) else ""
            enh_line = enhanced_lines[i] if i < len(enhanced_lines) else ""

            if orig_line != enh_line:
                if orig_line:
                    diff_lines.append(f"- {orig_line}")
                if enh_line:
                    diff_lines.append(f"+ {enh_line}")
            else:
                diff_lines.append(f"  {orig_line}")

        return '\n'.join(diff_lines)