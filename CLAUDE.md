# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Spec Kit is a toolkit for Spec-Driven Development (SDD), providing the `specify` CLI tool that bootstraps projects for structured development workflows. The project enables teams to create specifications that drive implementation rather than serving as afterthoughts.

## Important Files

### Project Specifications (`spec/`)
These files contain comprehensive project documentation following SDD methodology:

- **`spec/spec.md`**: Complete functional and non-functional requirements, user stories, and interface specifications
- **`spec/plan.md`**: Technical implementation plan including architecture, technology stack, and deployment strategy
- **`spec/data-model.md`**: Core entities, relationships, data flow, and validation rules for the CLI system
- **`spec/tasks.md`**: Detailed implementation breakdown with 23 tasks across 8 phases, dependencies, and parallel execution guidance
- **`spec/research.md`**: Technology selection rationale, architecture decisions, and future research areas
- **`spec/quickstart.md`**: Usage scenarios, test cases, troubleshooting guides, and performance benchmarks

These specification files serve as the authoritative source of truth for the project and should be consulted when making implementation decisions or understanding system requirements.

## Development Commands

### Fast Development Loop
```bash
# Run CLI directly (fastest feedback)
python -m src.specify_cli --help
python -m src.specify_cli init demo-project --ai claude --ignore-agent-tools --script sh

# Alternative: Script file style
python src/specify_cli/__init__.py init demo-project --script ps
```

### Isolated Development Environment
```bash
# Create & activate virtual env with uv
uv venv
source .venv/bin/activate  # Linux/Mac
# .venv\Scripts\Activate.ps1  # Windows PowerShell

# Install in editable mode
uv pip install -e .

# Now 'specify' entrypoint is available
specify --help
specify init demo-project --ai claude
```

### Testing with uvx
```bash
# Test from local source
uvx --from . specify init demo-uvx --ai copilot --ignore-agent-tools

# Test from specific branch
git push origin your-feature-branch
uvx --from git+https://github.com/github/spec-kit.git@your-feature-branch specify init demo-branch-test

# Test with absolute path (run from anywhere)
uvx --from /path/to/spec-kit specify init demo-anywhere --ai claude
```

### Build and Package
```bash
# Build wheel locally
uv build
ls dist/

# Import sanity check
python -c "import specify_cli; print('Import OK')"
```

### Debug Options
```bash
# Skip TLS validation (local testing only)
specify init demo --skip-tls --ai gemini --ignore-agent-tools

# Enable debug output
specify init demo --debug --ai claude
```

## Code Architecture

### Core Structure
- **src/specify_cli/__init__.py**: Single-file CLI application containing all functionality
- **templates/**: Template files for different AI assistants and project configurations
- **scripts/**: Shell scripts (bash/powershell) for project bootstrapping
- **memory/**: Documentation on project methodology and constitution templates

### Key Components

#### CLI Implementation (src/specify_cli/__init__.py)
- Built with **Typer** for command-line interface
- Uses **Rich** for terminal formatting and progress display
- **httpx** for GitHub API interactions and template downloads
- Cross-platform keyboard input with **readchar**
- SSL/TLS support via **truststore**

#### Main Commands
- `specify init <project-name>`: Bootstrap new projects with AI-specific templates
- `specify check`: Verify required tools are installed
- Interactive AI assistant selection (Claude, Gemini, Copilot, Cursor, Qwen, opencode, Codex, Windsurf)
- Script type selection (bash/PowerShell)

#### Template System
- Downloads latest release assets from GitHub API
- Extracts AI-specific templates based on selection
- Supports both new directory creation and in-place initialization (`--here`)
- Handles nested ZIP structures from GitHub releases

### Key Patterns

#### Progress Tracking
- `StepTracker` class provides tree-style progress visualization
- Live updating with Rich Live display
- Status tracking: pending, running, done, error, skipped

#### Cross-Platform Compatibility
- Detects Windows vs Unix-like systems for script selection
- Handles executable permissions on Unix systems
- Cross-platform keyboard input handling

#### Error Handling
- Comprehensive error handling for network operations
- Graceful fallbacks for missing tools
- Debug mode for troubleshooting

## Important Development Notes

### Dependencies
- Requires Python 3.11+
- Core dependencies: typer, rich, httpx[socks], platformdirs, readchar, truststore
- Built with Hatchling as build backend

### GitHub Integration
- Fetches templates from github/spec-kit releases
- Supports GitHub token authentication via CLI flag or environment variables
- Handles rate limiting and API errors

### AI Assistant Support
- Each AI assistant has specific template variants
- Tool availability checking before project creation
- Special handling for Claude CLI migration paths

### Template Structure and Slash Commands
Templates include sophisticated workflow automation:

#### Slash Commands (`templates/commands/`)
- `/constitution`: Create project governing principles and development guidelines
- `/specify`: Define requirements and user stories from natural language input
- `/plan`: Generate technical implementation plans with tech stack decisions
- `/tasks`: Create dependency-ordered, executable task breakdowns with parallel execution markers [P]
- `/implement`: Execute the complete implementation plan following TDD approach

#### Command Execution Flow
Each command includes:
- **Prerequisites checking**: Validates required files and project state
- **Script integration**: Runs bash/PowerShell scripts for environment setup
- **Structured outputs**: Creates organized documentation in feature directories
- **Dependency management**: Commands build on previous outputs (constitution → specify → plan → tasks → implement)

#### Supporting Infrastructure
- **Scripts directory**: Cross-platform shell scripts for project automation
- **Memory system**: Constitution templates and methodology documentation
- **Agent configurations**: AI-specific prompt templates and command definitions

## Spec-Driven Development Workflow

### The SDD Process
Spec-Driven Development inverts traditional development - specifications drive implementation rather than serving it:

1. **Constitution**: Establish project principles and constraints that guide all decisions
2. **Specification**: Transform ideas into comprehensive requirements through AI dialogue
3. **Planning**: Generate technical implementation plans with architecture decisions
4. **Task Breakdown**: Create executable, dependency-ordered tasks with parallel execution markers
5. **Implementation**: Execute tasks following TDD approach with continuous validation

### Key SDD Concepts
- **Intent-driven development**: Express requirements in natural language
- **Specification as source of truth**: Code serves specifications, not vice versa
- **Continuous regeneration**: Changes to specs automatically propagate to implementation
- **Parallel implementation**: Support creative exploration through multiple implementations
- **Test-first approach**: Tests generated from specifications before implementation

## Troubleshooting

### Development Issues
| Symptom | Fix |
|---------|-----|
| `ModuleNotFoundError: typer` | Run `uv pip install -e .` |
| Scripts not executable (Linux) | Re-run init or `chmod +x scripts/*.sh` |
| Git step skipped | You passed `--no-git` or Git not installed |
| Wrong script type downloaded | Pass `--script sh` or `--script ps` explicitly |
| TLS errors on corporate network | Try `--skip-tls` (not for production) |

### AI Assistant Issues
- Missing AI tools: Use `--ignore-agent-tools` to skip checks
- Network issues: Use `--debug` for detailed error output
- SSL/TLS problems: Use `--skip-tls` (not recommended)
- Git authentication: Install Git Credential Manager on Linux

### Claude CLI Special Cases
- Handles post-migration Claude CLI paths at `~/.claude/local/claude`
- Prioritizes local installation over PATH executables

### Cleaning Up
```bash
# Remove build artifacts and virtual environments
rm -rf .venv dist build *.egg-info
```