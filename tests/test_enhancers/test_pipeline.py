"""Tests for enhancement pipeline."""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch
from src.specify_analyzer.enhancers.pipeline import Enhancement<PERSON><PERSON>eline, EnhancementCache
from src.specify_analyzer.enhancers.base import (
    EnhancementConfig, EnhancementLevel, DocumentType, EnhancementResult
)


class MockProvider:
    """Mock AI provider for testing."""

    def __init__(self, config):
        self.config = config

    def enhance_document(self, content, doc_type, context, prompt):
        return EnhancementResult(
            success=True,
            enhanced_content=f"Enhanced: {content}",
            original_content=content,
            provider_used="mock",
            model_used="mock-model",
            tokens_used=100,
            cost_estimate=0.01,
            processing_time=1.0
        )

    def _estimate_cost(self, input_tokens, output_tokens):
        return 0.01


def test_pipeline_initialization():
    """Test pipeline initialization."""
    config = EnhancementConfig(
        provider="mock",
        model="mock-model",
        level=EnhancementLevel.MODERATE
    )

    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)

        with patch('src.specify_analyzer.enhancers.pipeline.OpenAIProvider', return_value=MockProvider(config)):
            # This will fail because we don't have the actual provider, but we can test config
            try:
                pipeline = EnhancementPipeline(config, cache_dir=cache_dir)
            except ValueError as e:
                assert "Unsupported provider" in str(e)


def test_enhancement_cache():
    """Test enhancement caching functionality."""
    cache_entry = EnhancementCache(
        content_hash="abc123",
        enhanced_content="Enhanced content",
        provider="openai",
        model="gpt-4",
        level="moderate",
        timestamp=**********.0
    )

    assert cache_entry.content_hash == "abc123"
    assert cache_entry.enhanced_content == "Enhanced content"
    assert cache_entry.provider == "openai"


def test_diff_generation():
    """Test diff generation functionality."""
    config = EnhancementConfig(
        provider="mock",
        model="mock-model",
        level=EnhancementLevel.MODERATE
    )

    # Create a mock pipeline to test diff generation
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)

        # Mock the provider creation to avoid the actual provider lookup
        with patch.object(EnhancementPipeline, '_create_provider', return_value=MockProvider(config)):
            pipeline = EnhancementPipeline(config, cache_dir=cache_dir)

            original = """# Original
Line 1
Line 2
Line 3"""

            enhanced = """# Enhanced
Line 1 (modified)
Line 2
Line 3
Line 4 (new)"""

            diff = pipeline.generate_diff(original, enhanced)

            assert "--- Original" in diff
            assert "+++ Enhanced" in diff
            assert "- Line 1" in diff
            assert "+ Line 1 (modified)" in diff
            assert "+ Line 4 (new)" in diff


@patch('src.specify_analyzer.enhancers.pipeline.OpenAIProvider')
def test_single_document_enhancement(mock_provider_class):
    """Test single document enhancement."""
    config = EnhancementConfig(
        provider="openai",
        model="gpt-4",
        level=EnhancementLevel.MODERATE
    )

    mock_provider = MockProvider(config)
    mock_provider_class.return_value = mock_provider

    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)
        pipeline = EnhancementPipeline(config, cache_dir=cache_dir)

        content = "# Test Document\nSome content here."
        project_context = {"primary_language": "python"}

        result = pipeline.enhance_single_document(
            content,
            DocumentType.SPEC,
            project_context
        )

        assert result.success
        assert result.enhanced_content == f"Enhanced: {content}"
        assert result.cost_estimate == 0.01


def test_cost_estimation():
    """Test cost estimation functionality."""
    config = EnhancementConfig(
        provider="openai",
        model="gpt-4",
        level=EnhancementLevel.MODERATE,
        cost_limit=1.0
    )

    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)

        with patch.object(EnhancementPipeline, '_create_provider', return_value=MockProvider(config)):
            pipeline = EnhancementPipeline(config, cache_dir=cache_dir)

            documents = {
                "spec.md": "Content 1",
                "plan.md": "Content 2",
                "data-model.md": "Content 3"
            }

            project_context = {"primary_language": "python"}

            # Mock the cost estimation method
            with patch.object(pipeline, '_estimate_total_cost', return_value=0.05):
                total_cost = pipeline._estimate_total_cost(documents, project_context)
                assert total_cost == 0.05


def test_cache_key_generation():
    """Test cache key generation."""
    config = EnhancementConfig(
        provider="openai",
        model="gpt-4",
        level=EnhancementLevel.MODERATE
    )

    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)

        with patch.object(EnhancementPipeline, '_create_provider', return_value=MockProvider(config)):
            pipeline = EnhancementPipeline(config, cache_dir=cache_dir)

            content = "Test content"
            doc_type = DocumentType.SPEC

            cache_key1 = pipeline._get_cache_key(content, doc_type)
            cache_key2 = pipeline._get_cache_key(content, doc_type)

            # Same content should generate same cache key
            assert cache_key1 == cache_key2

            # Different content should generate different cache key
            different_content = "Different content"
            cache_key3 = pipeline._get_cache_key(different_content, doc_type)
            assert cache_key1 != cache_key3


def test_validation_consistency():
    """Test document consistency validation."""
    config = EnhancementConfig(
        provider="openai",
        model="gpt-4",
        level=EnhancementLevel.MODERATE
    )

    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir)

        with patch.object(EnhancementPipeline, '_create_provider', return_value=MockProvider(config)):
            pipeline = EnhancementPipeline(config, cache_dir=cache_dir)

            # Test consistency validation
            spec_result = EnhancementResult(
                success=True,
                enhanced_content="API requirements for user management",
                original_content="User management"
            )

            previous_results = {
                "spec.md": spec_result
            }

            task_content = "Implement user interface without API"

            issues = pipeline._validate_consistency(
                DocumentType.TASKS,
                task_content,
                previous_results
            )

            assert "API requirements not reflected in tasks" in issues