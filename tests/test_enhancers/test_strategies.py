"""Tests for enhancement strategies."""

import pytest
from src.specify_analyzer.enhancers.strategies import (
    <PERSON><PERSON><PERSON>nhance<PERSON>, PlanEnhancer, DataModelEnhancer,
    TasksEnhancer, ResearchEnhancer, QuickstartEnhancer
)
from src.specify_analyzer.enhancers.base import DocumentType


def test_spec_enhancer():
    """Test SpecEnhancer functionality."""
    enhancer = SpecEnhancer()

    assert enhancer.document_type == DocumentType.SPEC

    # Test section identification
    content = """
    # Project Specification

    ## Functional Requirements
    - User login
    - User registration

    ## User Stories
    As a user, I want to login so that I can access my account.

    ## API Specifications
    POST /login
    """

    sections = enhancer.get_sections_to_enhance(content)
    assert "Functional Requirements" in sections
    assert "User Stories" in sections
    assert "API Specifications" in sections

    # Test preprocessing
    processed = enhancer.preprocess_content(content)
    assert "**User Story**:" in processed


def test_plan_enhancer():
    """Test PlanEnhancer functionality."""
    enhancer = PlanEnhancer()

    assert enhancer.document_type == DocumentType.PLAN

    # Test section identification
    content = """
    # Technical Plan

    ## Architecture
    Microservices architecture

    ## Technology Stack
    - nodejs for backend
    - reactjs for frontend

    ## Development Phases
    Phase 1: Setup
    Phase 2: Implementation

    ## Risk Management
    High risk: Database migration
    """

    sections = enhancer.get_sections_to_enhance(content)
    assert "Architecture" in sections
    assert "Technology Stack" in sections
    assert "Development Phases" in sections
    assert "Risk Management" in sections

    # Test technology standardization
    processed = enhancer.preprocess_content(content)
    assert "Node.js" in processed
    assert "React" in processed


def test_data_model_enhancer():
    """Test DataModelEnhancer functionality."""
    enhancer = DataModelEnhancer()

    assert enhancer.document_type == DocumentType.DATA_MODEL

    # Test section identification
    content = """
    # Data Model

    ## Entities
    User entity with fields

    ## Relationships
    One-to-many relationship

    ## Validation
    Field validation rules
    """

    sections = enhancer.get_sections_to_enhance(content)
    assert "Entities" in sections
    assert "Relationships" in sections
    assert "Validation" in sections

    # Test field type standardization
    content_with_types = "user_id: int, name: str, active: bool"
    processed = enhancer.preprocess_content(content_with_types)
    assert "Integer" in processed
    assert "String" in processed
    assert "Boolean" in processed


def test_tasks_enhancer():
    """Test TasksEnhancer functionality."""
    enhancer = TasksEnhancer()

    assert enhancer.document_type == DocumentType.TASKS

    # Test section identification
    content = """
    # Task Breakdown

    ## Phases
    Development phases

    ## Dependencies
    Task dependencies

    ## Time Estimates
    Task time estimates

    ## Parallel Execution
    Tasks that can run parallel [P]
    """

    sections = enhancer.get_sections_to_enhance(content)
    assert "Task Breakdown" in sections
    assert "Dependencies" in sections
    assert "Time Estimates" in sections
    assert "Parallel Execution" in sections

    # Test task formatting
    content_with_tasks = """
    - Setup database
    * Create API endpoints
    1. Write tests
    """

    processed = enhancer.preprocess_content(content_with_tasks)
    lines = processed.split('\n')
    numbered_lines = [line for line in lines if line.strip().startswith(('1.', '2.', '3.'))]
    assert len(numbered_lines) == 3


def test_research_enhancer():
    """Test ResearchEnhancer functionality."""
    enhancer = ResearchEnhancer()

    assert enhancer.document_type == DocumentType.RESEARCH

    # Test section identification
    content = """
    # Technology Research

    ## Technology Comparison
    nodejs vs python

    ## Architecture Patterns
    Microservices vs monolith

    ## Performance Analysis
    Benchmark results

    ## Decision Rationale
    Why we chose this approach
    """

    sections = enhancer.get_sections_to_enhance(content)
    assert "Technology Comparison" in sections
    assert "Architecture Patterns" in sections
    assert "Performance Analysis" in sections
    assert "Decision Rationale" in sections

    # Test technology name standardization
    content_with_tech = "We compared nodejs with python and reactjs"
    processed = enhancer.preprocess_content(content_with_tech)
    assert "Node.js" in processed
    assert "Python" in processed
    assert "React" in processed


def test_quickstart_enhancer():
    """Test QuickstartEnhancer functionality."""
    enhancer = QuickstartEnhancer()

    assert enhancer.document_type == DocumentType.QUICKSTART

    # Test section identification
    content = """
    # Quick Start Guide

    ## Setup Instructions
    Installation steps

    ## Code Examples
    Example code

    ## Common Scenarios
    Usage scenarios

    ## Troubleshooting
    Common issues and solutions
    """

    sections = enhancer.get_sections_to_enhance(content)
    assert "Setup Instructions" in sections
    assert "Code Examples" in sections
    assert "Common Scenarios" in sections
    assert "Troubleshooting" in sections

    # Test troubleshooting formatting
    content_with_issues = """
    Problem: Server won't start
    Solution: Check port availability
    Issue: Database connection failed
    Fix: Verify credentials
    """

    processed = enhancer.postprocess_content(content_with_issues)
    assert "⚠️ **Problem**:" in processed
    assert "✅ **Solution**:" in processed


def test_base_strategy_section_extraction():
    """Test base strategy section extraction."""
    enhancer = SpecEnhancer()

    content = """
# Main Title

Some introduction text.

## Section 1

Content for section 1.

### Subsection 1.1

Subsection content.

## Section 2

Content for section 2.
"""

    sections = enhancer._extract_sections(content)
    assert "# Main Title" in sections
    assert "## Section 1" in sections
    assert "### Subsection 1.1" in sections
    assert "## Section 2" in sections


def test_markdown_validation():
    """Test markdown structure validation."""
    enhancer = SpecEnhancer()

    # Valid markdown with headers
    valid_content = """
# Title
## Section
Content here
"""
    assert enhancer._validate_markdown_structure(valid_content)

    # Invalid markdown without headers
    invalid_content = "Just plain text without headers"
    assert not enhancer._validate_markdown_structure(invalid_content)