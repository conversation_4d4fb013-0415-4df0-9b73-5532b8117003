"""Tests for base enhancement functionality."""

import pytest
from unittest.mock import Mock, patch
from src.specify_analyzer.enhancers.base import (
    EnhancementConfig, EnhancementLevel, DocumentType,
    EnhancementResult, RateLimiter, BaseAIProvider
)


class MockAIProvider(BaseAIProvider):
    """Mock AI provider for testing."""

    def _requires_api_key(self) -> bool:
        return False

    def _get_api_key_env_var(self) -> str:
        return "TEST_API_KEY"

    def _estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        return 0.01

    def _make_api_call(self, messages):
        return {
            "choices": [{"message": {"content": "Enhanced content"}}],
            "usage": {"completion_tokens": 100}
        }

    def _extract_content(self, response):
        return response["choices"][0]["message"]["content"]

    def _extract_token_usage(self, response):
        return response["usage"]["completion_tokens"]


def test_enhancement_config():
    """Test enhancement configuration creation."""
    config = EnhancementConfig(
        provider="test",
        model="test-model",
        level=EnhancementLevel.MODERATE
    )

    assert config.provider == "test"
    assert config.model == "test-model"
    assert config.level == EnhancementLevel.MODERATE
    assert config.max_tokens == 4000
    assert config.temperature == 0.3


def test_enhancement_levels():
    """Test enhancement level enum."""
    assert EnhancementLevel.LIGHT.value == "light"
    assert EnhancementLevel.MODERATE.value == "moderate"
    assert EnhancementLevel.COMPREHENSIVE.value == "comprehensive"


def test_document_types():
    """Test document type enum."""
    assert DocumentType.SPEC.value == "spec"
    assert DocumentType.PLAN.value == "plan"
    assert DocumentType.DATA_MODEL.value == "data_model"


def test_enhancement_result():
    """Test enhancement result structure."""
    result = EnhancementResult(
        success=True,
        enhanced_content="Enhanced content",
        original_content="Original content",
        provider_used="test",
        model_used="test-model",
        tokens_used=100,
        cost_estimate=0.01,
        processing_time=1.5
    )

    assert result.success
    assert result.enhanced_content == "Enhanced content"
    assert result.tokens_used == 100
    assert result.cost_estimate == 0.01


def test_rate_limiter():
    """Test rate limiter functionality."""
    limiter = RateLimiter(max_requests_per_minute=2)

    # Should allow first request immediately
    start_time = limiter.requests
    limiter.wait_if_needed()
    assert len(limiter.requests) == 1

    # Should allow second request immediately
    limiter.wait_if_needed()
    assert len(limiter.requests) == 2


def test_mock_ai_provider():
    """Test mock AI provider functionality."""
    config = EnhancementConfig(
        provider="test",
        model="test-model",
        level=EnhancementLevel.MODERATE
    )

    provider = MockAIProvider(config)

    result = provider.enhance_document(
        content="Test content",
        document_type=DocumentType.SPEC,
        project_context={"language": "python"},
        enhancement_prompt="Enhance this document"
    )

    assert result.success
    assert result.enhanced_content == "Enhanced content"
    assert result.tokens_used == 100
    assert result.cost_estimate == 0.01


def test_base_provider_prepare_messages():
    """Test message preparation in base provider."""
    config = EnhancementConfig(
        provider="test",
        model="test-model",
        level=EnhancementLevel.MODERATE
    )

    provider = MockAIProvider(config)

    messages = provider._prepare_messages(
        content="Test content",
        document_type=DocumentType.SPEC,
        project_context={"primary_language": "python", "frameworks": ["fastapi"]},
        enhancement_prompt="Enhance this"
    )

    assert len(messages) == 2
    assert messages[0]["role"] == "system"
    assert messages[1]["role"] == "user"
    assert "python" in messages[0]["content"]
    assert "fastapi" in messages[0]["content"]
    assert "Test content" in messages[1]["content"]


def test_token_estimation():
    """Test token estimation."""
    config = EnhancementConfig(
        provider="test",
        model="test-model",
        level=EnhancementLevel.MODERATE
    )

    provider = MockAIProvider(config)

    # Test rough token estimation (4 chars per token)
    text = "This is a test sentence with multiple words."
    estimated_tokens = provider._estimate_tokens(text)
    expected_tokens = len(text) // 4
    assert estimated_tokens == expected_tokens